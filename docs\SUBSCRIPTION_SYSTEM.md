# Enhanced Subscription System

This document describes the enhanced subscription system that implements scheduled plan changes for a better user experience.

## Overview

The enhanced subscription system provides two different flows for plan changes:

1. **Free to Paid**: Immediate activation after payment
2. **Paid to Paid**: Scheduled activation at the end of current billing period

## Key Features

### 1. Immediate Activation (Free to Paid)
- When a user on a free plan upgrades to any paid plan
- Payment is processed immediately
- New plan takes effect immediately
- User gets instant access to premium features

### 2. Scheduled Activation (Paid to Paid)
- When a user on a paid plan changes to another paid plan (upgrade/downgrade)
- Payment is processed immediately
- New plan is scheduled to take effect at the end of current billing period
- User continues with current plan until expiry
- No service interruption

## Database Schema

### New Tables

#### `scheduled_plan_changes`
```sql
CREATE TABLE scheduled_plan_changes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
    current_plan_type TEXT NOT NULL,
    current_billing_cycle TEXT NOT NULL,
    new_plan_type TEXT NOT NULL,
    new_billing_cycle TEXT NOT NULL,
    scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
    amount_paid DECIMAL(10, 2) NOT NULL,
    payment_reference TEXT NOT NULL,
    payment_status TEXT DEFAULT 'completed',
    status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'applied', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Enhanced `subscriptions` Table
New fields added:
- `scheduled_plan_type`: The plan type scheduled to take effect
- `scheduled_billing_cycle`: The billing cycle scheduled to take effect
- `scheduled_change_date`: When the scheduled change will take effect
- `scheduled_change_amount`: Amount paid for the scheduled change
- `scheduled_change_reference`: Payment reference for the scheduled change
- `has_scheduled_change`: Boolean flag indicating if there's a scheduled change

## Services

### 1. Enhanced Subscription Service (`enhancedSubscriptionService.ts`)

#### Key Functions:
- `initializePlanChange()`: Handles plan change initialization
- `processPlanChange()`: Processes plan changes after payment verification
- `isOnPaidPlan()`: Checks if user is currently on a paid plan
- `getPlanChangeEffectiveDate()`: Determines when plan change should take effect
- `getScheduledPlanChanges()`: Gets user's scheduled plan changes
- `cancelScheduledPlanChange()`: Cancels a scheduled plan change

### 2. Scheduled Plan Processor (`scheduledPlanProcessor.ts`)

#### Key Functions:
- `processScheduledPlanChanges()`: Processes all due scheduled changes
- `runScheduledPlanProcessor()`: Main function for cron jobs
- `cleanupOldScheduledChanges()`: Removes old applied changes
- `getScheduledPlanChangeStats()`: Gets statistics about scheduled changes

## Components

### 1. ScheduledPlanChanges Component
- Displays user's scheduled plan changes
- Allows cancellation of scheduled changes
- Shows effective dates and payment information

### 2. Enhanced SubscriptionPlans Component
- Uses new enhanced subscription service
- Shows appropriate messaging for immediate vs scheduled changes
- Handles both upgrade flows seamlessly

## User Experience Flow

### Free to Paid Upgrade
1. User selects a paid plan
2. System detects user is on free plan
3. Payment is processed
4. Plan is activated immediately
5. User gets instant access to features

### Paid to Paid Change
1. User selects a different paid plan
2. System detects user is on paid plan
3. Payment is processed immediately
4. Change is scheduled for end of current period
5. User sees confirmation with effective date
6. Current plan continues until expiry
7. New plan activates automatically on scheduled date

## Cron Job Setup

### Automatic Processing
Set up a cron job to call the Supabase Edge Function:
```bash
# Run every hour
0 * * * * curl -X POST https://your-supabase-project.supabase.co/functions/v1/process-scheduled-plans \
  --header 'Authorization: Bearer YOUR_CRON_SECRET' \
  --header 'Content-Type: application/json'
```

### Manual Processing
You can also manually trigger processing via the Edge Function:
```bash
curl -X POST https://your-supabase-project.supabase.co/functions/v1/process-scheduled-plans \
  --header 'Authorization: Bearer YOUR_CRON_SECRET' \
  --header 'Content-Type: application/json'
```

### Local Development
For local development with Supabase CLI:
```bash
curl -X POST http://localhost:54321/functions/v1/process-scheduled-plans \
  --header 'Authorization: Bearer YOUR_CRON_SECRET' \
  --header 'Content-Type: application/json'
```

## Security Considerations

1. **Payment Verification**: All payments are verified with Paystack before processing
2. **Transaction Logging**: All subscription changes are logged for audit purposes
3. **User Authorization**: Only authenticated users can make plan changes
4. **Reference Validation**: Payment references are validated to prevent fraud

## Error Handling

### Payment Failures
- Failed payments don't create scheduled changes
- Users are notified of payment failures
- No plan changes occur without successful payment

### Processing Failures
- Failed scheduled changes are logged but not applied
- System continues processing other scheduled changes
- Manual intervention may be required for failed changes

## Monitoring

### Statistics Available
- Number of scheduled changes
- Number of applied changes
- Number of cancelled changes
- Total revenue from scheduled changes

### Logging
- All plan changes are logged to `subscription_transactions` table
- Processing results are logged for monitoring
- Error details are captured for debugging

## Migration

### From Legacy System
1. Run the migration: `migrations/add_scheduled_plan_changes.sql`
2. Update subscription components to use enhanced service
3. Set up cron job for processing scheduled changes
4. Test with small group before full rollout

### Backward Compatibility
- Legacy subscription flows continue to work
- New flows are detected by payment reference prefixes
- Gradual migration is supported

## Testing

### Test Scenarios
1. Free to paid upgrade (immediate)
2. Paid to paid upgrade (scheduled)
3. Paid to paid downgrade (scheduled)
4. Scheduled change cancellation
5. Automatic processing of due changes
6. Payment failure handling

### Test Data
Use payment references with specific prefixes:
- `IMM-`: Immediate activation
- `SCH-`: Scheduled activation
- Legacy references: Use legacy flow

## Configuration

### Environment Variables
- `VITE_PAYSTACK_PUBLIC_KEY`: Paystack public key
- `PAYSTACK_SECRET_KEY`: Paystack secret key (server-side)
- `CRON_SECRET`: Optional secret for cron job authentication

### Plan Prices
Configured in `secureSubscriptionService.ts`:
```typescript
export const PLAN_PRICES = {
  freelancer: { monthly: 49, yearly: 490 },
  business: { monthly: 99, yearly: 990 },
  enterprise: { monthly: 199, yearly: 1990 }
};
```

## Support

### Common Issues
1. **Scheduled change not applied**: Check cron job is running
2. **Payment processed but no change**: Check payment reference format
3. **User can't see scheduled change**: Refresh subscription context

### Troubleshooting
1. Check `subscription_transactions` table for payment logs
2. Check `scheduled_plan_changes` table for scheduled changes
3. Verify cron job is running regularly
4. Check Paystack webhook configuration
