// This script helps with direct installation on desktop browsers
(function() {
  // Only run on desktop browsers
  if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
    return;
  }
  
  // Check if we're in a standalone mode already
  if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
    return;
  }
  
  // Listen for our custom event
  document.addEventListener('chrome-pwa-install-trigger', function(e) {
    console.log('Install trigger event received:', e);
    
    // Try to highlight the install button in Chrome
    try {
      // Scroll to top to make address bar visible
      window.scrollTo(0, 0);
      
      // Focus the window
      window.focus();
      
      // Blur any active element
      if (document.activeElement instanceof HTMLElement) {
        document.activeElement.blur();
      }
      
      // Create a visual indicator pointing to the address bar
      const indicator = document.createElement('div');
      indicator.style.position = 'fixed';
      indicator.style.top = '0';
      indicator.style.right = '80px';
      indicator.style.width = '0';
      indicator.style.height = '0';
      indicator.style.borderLeft = '10px solid transparent';
      indicator.style.borderRight = '10px solid transparent';
      indicator.style.borderBottom = '10px solid #00A651';
      indicator.style.zIndex = '999999';
      indicator.style.animation = 'bounce 1s infinite';
      indicator.style.pointerEvents = 'none';
      
      // Add animation
      const style = document.createElement('style');
      style.textContent = `
        @keyframes bounce {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-10px); }
        }
      `;
      document.head.appendChild(style);
      
      // Add to body
      document.body.appendChild(indicator);
      
      // Remove after 5 seconds
      setTimeout(function() {
        if (indicator.parentNode) {
          indicator.parentNode.removeChild(indicator);
        }
      }, 5000);
    } catch (error) {
      console.error('Error creating install indicator:', error);
    }
  });
  
  // DISABLED: Listen for beforeinstallprompt event
  // This is now handled by UniversalInstallButton to prevent duplication
  /*
  window.addEventListener('beforeinstallprompt', function(e) {
    // Store the event for later use
    window.deferredPrompt = e;

    console.log('beforeinstallprompt event captured by desktop helper');

    // Prevent the mini-infobar from appearing on mobile
    e.preventDefault();

    // Dispatch a custom event to notify React components
    const customEvent = new CustomEvent('pwa-install-available', {
      detail: { prompt: e }
    });
    window.dispatchEvent(customEvent);
  });
  */
  
  // Listen for appinstalled event
  window.addEventListener('appinstalled', function(e) {
    console.log('App was installed', e);
    
    // Clear the deferredPrompt
    window.deferredPrompt = null;
    
    // Hide any install buttons
    const installButton = document.getElementById('universal-install-button');
    if (installButton) {
      installButton.style.display = 'none';
    }
  });
})();
