<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <!-- Set proper MIME types -->
    <staticContent>
      <remove fileExtension=".json" />
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <remove fileExtension=".woff" />
      <mimeMap fileExtension=".woff" mimeType="font/woff" />
      <remove fileExtension=".woff2" />
      <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
      <remove fileExtension=".js" />
      <mimeMap fileExtension=".js" mimeType="application/javascript" />
      <remove fileExtension=".mjs" />
      <mimeMap fileExtension=".mjs" mimeType="application/javascript" />
      <remove fileExtension=".ts" />
      <mimeMap fileExtension=".ts" mimeType="application/javascript" />
      <remove fileExtension=".tsx" />
      <mimeMap fileExtension=".tsx" mimeType="application/javascript" />
    </staticContent>
    
    <!-- URL Rewrite for SPA -->
    <rewrite>
      <rules>
        <rule name="SPA Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/api/" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
      </rules>
    </rewrite>
    
    <!-- HTTP Headers -->
    <httpProtocol>
      <customHeaders>
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-XSS-Protection" value="1; mode=block" />
        <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
        <add name="Service-Worker-Allowed" value="/" />
      </customHeaders>
    </httpProtocol>
    
    <!-- Cache Control -->
    <location path="service-worker.js">
      <system.webServer>
        <httpProtocol>
          <customHeaders>
            <add name="Cache-Control" value="no-cache, no-store, must-revalidate" />
            <add name="Pragma" value="no-cache" />
            <add name="Expires" value="0" />
          </customHeaders>
        </httpProtocol>
      </system.webServer>
    </location>
  </system.webServer>
</configuration>
