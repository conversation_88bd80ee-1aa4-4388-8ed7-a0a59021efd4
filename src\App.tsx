
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import ScrollToTop from "./components/ScrollToTop";
import { lazy, Suspense } from "react"; // Import lazy and Suspense
import { SessionProvider } from "./contexts/SessionContext";
import { SubscriptionProvider } from "./contexts/SubscriptionContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import { ThemeProvider } from "./contexts/ThemeContext";

import UniversalInstallButton from "./components/pwa/UniversalInstallButton";
import PWAInstallPrompt from "./components/pwa/PWAInstallPrompt";


import AuthGuard from "./components/AuthGuard";
import AdminGuard from "./components/guards/AdminGuard";
import SubscriptionGuard from "./components/guards/SubscriptionGuard";
import { SubscriptionGuard as SecureSubscriptionGuard } from "./middleware/subscriptionMiddleware";

// Pages
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Auth from "./pages/Auth";
import AuthCallback from "./pages/AuthCallback";
import Dashboard from "./pages/Dashboard";
import Invoices from "./pages/Invoices";
import CreateInvoice from "./pages/CreateInvoice";
import InvoiceDetail from "./pages/InvoiceDetail";
import PublicInvoice from "./pages/PublicInvoice";
import Clients from "./pages/Clients";
import Reports from "./pages/Reports";
import Settings from "./pages/Settings";
import Profile from "./pages/Profile";
import Features from "./pages/Features";
import About from "./pages/About";
import Contact from "./pages/Contact";
import Pricing from "./pages/Pricing";
import SubscriptionCallback from "./pages/SubscriptionCallback";
import EditClient from "./pages/EditClient";
import ClientDetail from "./pages/ClientDetail";
import AdvancedAnalytics from "./pages/AdvancedAnalytics";
import Analytics from "./pages/Analytics";
import Subscription from "./pages/Subscription";
import SecureSubscriptionManagement from "./pages/SecureSubscriptionManagement";
import GraSettings from "./pages/GraSettings";
import GraApiTest from "./pages/GraApiTest";
import Notifications from "./pages/Notifications";
import PaidInvoices from "./pages/PaidInvoices";
import TestPaidInvoices from "./pages/TestPaidInvoices";
import HelpCenter from "./pages/HelpCenter";
import SecurityPolicy from "./pages/SecurityPolicy";
import Roadmap from "./pages/Roadmap";
import Blog from "./pages/Blog";
import Careers from "./pages/Careers";
import Press from "./pages/Press";
import Terms from "./pages/Terms";
import Privacy from "./pages/Privacy";
import Compliance from "./pages/Compliance";
import Guides from "./pages/Guides";
import ApiDocs from "./pages/ApiDocs";
import Community from "./pages/Community";
import Faqs from "./pages/Faqs";
import Demo from "./pages/Demo";
import AdminDashboard from "./pages/AdminDashboard";
import AdminUsers from "./pages/admin/Users";
import AdminPayments from "./pages/admin/Payments";
import AdminBlog from "./pages/admin/Blog";
import AdminDemoBookings from "./pages/admin/DemoBookings";
import AdminFAQs from "./pages/admin/FAQs";
import AdminRoadmap from "./pages/admin/Roadmap";
import AdminPress from "./pages/admin/Press";
import AdminCareers from "./pages/admin/Careers";
import AdminHelpCenter from "./pages/admin/HelpCenter";
import AdminGuides from "./pages/admin/Guides";
import AdminApiDocs from "./pages/admin/ApiDocs";
import AdminCommunity from "./pages/admin/Community";
import AdminTerms from "./pages/admin/Terms";
import AdminPrivacy from "./pages/admin/Privacy";
import AdminGRACompliance from "./pages/admin/GRACompliance";
import AdminSecurity from "./pages/admin/Security";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <SessionProvider>
      <ThemeProvider>
        <SubscriptionProvider>
          <NotificationProvider>
            <TooltipProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
                <ScrollToTop />
                {/* PWA Install Components - Only UniversalInstallButton is active */}
                {/* UniversalInstallButton handles all platforms with coordination */}
                <Suspense fallback={null}>
                  <UniversalInstallButton />
                </Suspense>

                
                <Routes>
            {/* Public routes */}
            <Route path="/" element={<Index />} />
            <Route path="/features" element={<Features />} />
            <Route path="/about" element={<About />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/pricing" element={<Pricing />} />
            <Route path="/roadmap" element={<Roadmap />} />
            <Route path="/blog" element={<Blog />} />
            <Route path="/careers" element={<Careers />} />
            <Route path="/press" element={<Press />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/demo" element={<Demo />}/>
            <Route path="/compliance" element={<Compliance />} />
            <Route path="/help-center" element={<HelpCenter />} />
            <Route path="/guides" element={<Guides />} />
            <Route path="/api-docs" element={<ApiDocs />} />
            <Route path="/community" element={<Community />} />
            <Route path="/faqs" element={<Faqs />} />
            <Route path="/security-policy" element={<SecurityPolicy />} />
            <Route path="/auth" element={<Auth />} />
            <Route path="/auth/callback" element={<AuthCallback />} />
            <Route path="/subscription/callback" element={<SubscriptionCallback />} />
            <Route path="/public/invoice/:token" element={<PublicInvoice />} />

            {/* Protected routes */}
            <Route path="/dashboard" element={
              <AuthGuard>
                <Dashboard />
              </AuthGuard>
            } />
            <Route path="/invoices" element={
              <AuthGuard>
                <Invoices />
              </AuthGuard>
            } />
            <Route path="/invoices/create" element={
              <AuthGuard>
                <CreateInvoice />
              </AuthGuard>
            } />
            <Route path="/invoices/:id" element={
              <AuthGuard>
                <InvoiceDetail />
              </AuthGuard>
            } />
            <Route path="/clients" element={
              <AuthGuard>
                <Clients />
              </AuthGuard>
            } />
            <Route path="/clients/:id" element={
              <AuthGuard>
                <ClientDetail />
              </AuthGuard>
            } />
            <Route path="/clients/:id/edit" element={
              <AuthGuard>
                <EditClient />
              </AuthGuard>
            } />
            <Route path="/reports" element={
              <AuthGuard>
                <Reports />
              </AuthGuard>
            } />
            <Route path="/analytics" element={
              <AuthGuard>
                <SubscriptionGuard requiredFeature="analytics">
                  <Analytics />
                </SubscriptionGuard>
              </AuthGuard>
            } />
            <Route path="/advanced-analytics" element={
              <AuthGuard>
                <SubscriptionGuard requiredTier="business" requiredFeature="advanced_analytics">
                  <AdvancedAnalytics />
                </SubscriptionGuard>
              </AuthGuard>
            } />
            <Route path="/subscription" element={
              <AuthGuard>
                <Subscription />
              </AuthGuard>
            } />
            <Route path="/subscription/manage" element={
              <AuthGuard>
                <SecureSubscriptionManagement />
              </AuthGuard>
            } />
            <Route path="/settings" element={
              <AuthGuard>
                <Settings />
              </AuthGuard>
            } />
            <Route path="/profile" element={
              <AuthGuard>
                <Profile />
              </AuthGuard>
            } />

            <Route path="/gra-settings" element={
              <AuthGuard>
                <GraSettings />
              </AuthGuard>
            } />

            <Route path="/gra-api-test" element={
              <AuthGuard>
                <GraApiTest />
              </AuthGuard>
            } />

            <Route path="/notifications" element={
              <AuthGuard>
                <Notifications />
              </AuthGuard>
            } />

            <Route path="/paid-invoices" element={
              <AuthGuard>
                <PaidInvoices />
              </AuthGuard>
            } />

            <Route path="/test-paid-invoices" element={
              <AuthGuard>
                <TestPaidInvoices />
              </AuthGuard>
            } />

            {/* Admin routes */}
            <Route path="/admin" element={
              <AdminGuard>
                <AdminDashboard />
              </AdminGuard>
            } />
            <Route path="/admin/users" element={
              <AdminGuard>
                <AdminUsers />
              </AdminGuard>
            } />
            <Route path="/admin/payments" element={
              <AdminGuard>
                <AdminPayments />
              </AdminGuard>
            } />
            <Route path="/admin/blog" element={
              <AdminGuard>
                <AdminBlog />
              </AdminGuard>
            } />
            <Route path="/admin/demo-bookings" element={
              <AdminGuard>
                <AdminDemoBookings />
              </AdminGuard>
            } />
            <Route path="/admin/faqs" element={
              <AdminGuard>
                <AdminFAQs />
              </AdminGuard>
            } />
            <Route path="/admin/roadmap" element={
              <AdminGuard>
                <AdminRoadmap />
              </AdminGuard>
            } />
            <Route path="/admin/press" element={
              <AdminGuard>
                <AdminPress />
              </AdminGuard>
            } />
            <Route path="/admin/careers" element={
              <AdminGuard>
                <AdminCareers />
              </AdminGuard>
            } />
            <Route path="/admin/help-center" element={
              <AdminGuard>
                <AdminHelpCenter />
              </AdminGuard>
            } />
            <Route path="/admin/guides" element={
              <AdminGuard>
                <AdminGuides />
              </AdminGuard>
            } />
            <Route path="/admin/api-docs" element={
              <AdminGuard>
                <AdminApiDocs />
              </AdminGuard>
            } />
            <Route path="/admin/community" element={
              <AdminGuard>
                <AdminCommunity />
              </AdminGuard>
            } />
            <Route path="/admin/terms" element={
              <AdminGuard>
                <AdminTerms />
              </AdminGuard>
            } />
            <Route path="/admin/privacy" element={
              <AdminGuard>
                <AdminPrivacy />
              </AdminGuard>
            } />
            <Route path="/admin/gra-compliance" element={
              <AdminGuard>
                <AdminGRACompliance />
              </AdminGuard>
            } />
            <Route path="/admin/security" element={
              <AdminGuard>
                <AdminSecurity />
              </AdminGuard>
            } />

            {/* Catch-all route for 404 */}
            <Route path="*" element={<NotFound />} />
              </Routes>
                </BrowserRouter>
              </TooltipProvider>
            </NotificationProvider>
          </SubscriptionProvider>
        </ThemeProvider>
      </SessionProvider>
    </QueryClientProvider>

);

export default App;
