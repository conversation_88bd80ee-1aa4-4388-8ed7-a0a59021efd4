
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { ArrowRight, CheckCircle, Star, Shield, Clock, ChevronDown, FileText, Building, User, MapPin, Mail, Phone } from 'lucide-react';
import { motion } from 'framer-motion';
import AnimatedSection from './AnimatedSection';
import { AnimatedButton } from './ui/animated-button';

const Hero: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Trigger animations after component mounts
    setIsVisible(true);
  }, []);

  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-white via-gray-50 to-white dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 py-20 md:py-28">
      {/* Animated background elements */}
      <div className="absolute inset-0 ghana-pattern opacity-10 z-0"></div>
      <div className="absolute top-20 left-10 w-64 h-64 bg-ghana-gold/20 dark:bg-ghana-gold/30 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-72 h-72 bg-ghana-green/20 dark:bg-ghana-green/30 rounded-full blur-3xl animate-pulse"></div>

      {/* Small decorative elements */}
      <div className="absolute top-40 left-[15%] w-6 h-6 bg-ghana-gold/40 dark:bg-ghana-gold/50 rounded-full animate-float"></div>
      <div className="absolute top-60 right-[20%] w-4 h-4 bg-ghana-green/40 dark:bg-ghana-green/50 rounded-full animate-bounce"></div>
      <div className="absolute bottom-40 left-[30%] w-5 h-5 bg-ghana-red/30 dark:bg-ghana-red/40 rounded-full animate-bounce"></div>

      <div className="container relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div className={`flex flex-col gap-6 transition-all duration-1000 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
            <div className="inline-block bg-ghana-gold/20 dark:bg-ghana-gold/30 px-4 py-1.5 rounded-full animate-pulse">
              <span className="text-sm font-semibold text-ghana-black dark:text-ghana-gold flex items-center">
                <Star className="h-4 w-4 mr-1.5 text-ghana-gold" />
                GRA VAT-Compliant Invoicing
              </span>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-display leading-tight dark:text-foreground">
              <span className="text-ghana-green dark:text-ghana-gold relative">
                Simplified
                <span className="absolute -bottom-2 left-0 w-full h-1 bg-ghana-gold/60 rounded-full"></span>
              </span>{" "}
              Invoicing for Ghanaian Businesses
            </h1>

            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-lg">
              Easily create, manage, and track GRA VAT-compliant invoices. Perfect for freelancers and businesses across Ghana.
            </p>

            <div className="flex flex-col sm:flex-row flex-wrap gap-4 mt-4">
              <Link to="/auth" className="group">
                <Button className="bg-ghana-green hover:bg-ghana-green/90 text-white px-8 py-6 text-lg w-full sm:w-auto transition-all duration-300 transform group-hover:scale-105 shadow-lg hover:shadow-xl dark:bg-ghana-green/90 dark:hover:bg-ghana-green">
                  Get Started Free
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
              <Link to="/demo">
                <Button variant="outline" className="px-8 py-6 text-lg border-2 border-ghana-green text-ghana-green hover:bg-ghana-green hover:text-white transition-all duration-300 dark:border-ghana-gold dark:text-ghana-gold dark:hover:bg-ghana-gold/80 dark:hover:text-black">
                  See Demo
                </Button>
              </Link>
            </div>

            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-4 p-4 bg-white/80 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-100 dark:border-gray-700 shadow-sm">
              <div className="flex -space-x-3">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className={`w-10 h-10 rounded-full border-2 border-white dark:border-gray-700 bg-ghana-gold-light dark:bg-ghana-gold flex items-center justify-center text-xs font-semibold shadow-md transition-transform hover:scale-110 hover:z-10`}>
                    {i}
                  </div>
                ))}
              </div>
              <div>
                <div className="flex items-center">
                  <span className="text-sm font-medium text-gray-800 dark:text-gray-200">Trusted by 2,000+ businesses in Ghana</span>
                  <div className="flex ml-2">
                    {[1, 2, 3, 4, 5].map((i) => (
                      <Star key={i} className="h-3.5 w-3.5 text-ghana-gold fill-ghana-gold" />
                    ))}
                  </div>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Join the growing community of satisfied users</p>
              </div>
            </div>

            <div className="flex flex-wrap gap-4 mt-2">
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <CheckCircle className="h-4 w-4 mr-1.5 text-ghana-green dark:text-ghana-gold" />
                <span>GRA Compliant</span>
              </div>
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <Shield className="h-4 w-4 mr-1.5 text-ghana-green dark:text-ghana-gold" />
                <span>Secure Platform</span>
              </div>
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                <Clock className="h-4 w-4 mr-1.5 text-ghana-green dark:text-ghana-gold" />
                <span>Quick Setup</span>
              </div>
            </div>
          </div>

          <div className={`relative transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
            {/* Decorative elements */}
            <div className="absolute -top-12 -left-12 w-40 h-40 bg-ghana-gold rounded-full opacity-20 dark:opacity-30 blur-3xl"></div>
            <div className="absolute -bottom-12 -right-12 w-40 h-40 bg-ghana-green rounded-full opacity-20 dark:opacity-30 blur-3xl"></div>

            {/* Invoice preview with animation */}
            <div className="relative bg-white dark:bg-gray-900 rounded-xl shadow-2xl overflow-hidden border border-gray-100 dark:border-gray-700 transition-all duration-500 hover:shadow-ghana-green/20 dark:hover:shadow-ghana-gold/20 hover:-translate-y-1">
              {/* Invoice Header */}
              <div className="bg-gradient-to-r from-ghana-green to-ghana-green-dark dark:from-ghana-green/90 dark:to-ghana-green-dark/90 text-white p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <div className="bg-white dark:bg-gray-200 rounded-full p-1.5">
                        <FileText className="h-4 w-4 text-ghana-green dark:text-ghana-green" />
                      </div>
                      <h2 className="text-xl font-bold tracking-tight">INVOICE</h2>
                    </div>
                    <div className="bg-white/20 dark:bg-white/30 rounded-lg px-3 py-1 inline-block">
                      <p className="text-white font-medium text-sm"># INV-001234</p>
                    </div>
                  </div>

                  <div className="text-center bg-white dark:bg-gray-800 rounded-lg p-2 shadow-md">
                    <div className="flex items-center gap-1 mb-1">
                      <Shield className="h-3 w-3 text-ghana-green dark:text-ghana-gold" />
                      <p className="text-xs text-ghana-green dark:text-ghana-gold font-semibold">GRA Verified</p>
                    </div>
                    <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 mx-auto mb-1 relative overflow-hidden">
                      {/* Animated QR code effect */}
                      <div className="absolute inset-0 grid grid-cols-3 grid-rows-3 gap-0.5">
                        {Array(9).fill(0).map((_, i) => (
                          <div key={i} className={`bg-gray-800 dark:bg-gray-900 ${Math.random() > 0.5 ? 'opacity-100' : 'opacity-30'}`}></div>
                        ))}
                      </div>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400">Scan to verify</p>
                  </div>
                </div>
              </div>

              <div className="p-4 dark:bg-gray-900">
                {/* Company and Client Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-100 dark:border-gray-700">
                    <h3 className="font-bold text-gray-700 dark:text-white mb-2 flex items-center gap-2 text-sm uppercase tracking-wider">
                      <Building className="h-4 w-4 text-ghana-green dark:text-ghana-gold" />
                      <span>From</span>
                    </h3>
                    <div className="space-y-1">
                      <p className="font-bold text-sm dark:text-white">TechCorp Solutions</p>
                      <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400 text-xs">
                        <FileText className="h-3 w-3 flex-shrink-0 text-gray-400 dark:text-gray-500" />
                        <p>TIN: C0000123456</p>
                      </div>
                      <div className="flex items-start gap-1 text-gray-600 dark:text-gray-400 text-xs">
                        <MapPin className="h-3 w-3 mt-0.5 flex-shrink-0 text-gray-400 dark:text-gray-500" />
                        <p>Accra, Ghana</p>
                      </div>
                      <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400 text-xs">
                        <Mail className="h-3 w-3 flex-shrink-0 text-gray-400 dark:text-gray-500" />
                        <p><EMAIL></p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-100 dark:border-gray-700">
                    <h3 className="font-bold text-gray-700 dark:text-white mb-2 flex items-center gap-2 text-sm uppercase tracking-wider">
                      <User className="h-4 w-4 text-ghana-green dark:text-ghana-gold" />
                      <span>Bill To</span>
                    </h3>
                    <div className="space-y-1">
                      <p className="font-bold text-sm dark:text-white">ABC Enterprise Ltd</p>
                      <div className="flex items-start gap-1 text-gray-600 dark:text-gray-400 text-xs">
                        <MapPin className="h-3 w-3 mt-0.5 flex-shrink-0 text-gray-400 dark:text-gray-500" />
                        <p>Kumasi, Ghana</p>
                      </div>
                      <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400 text-xs">
                        <Mail className="h-3 w-3 flex-shrink-0 text-gray-400 dark:text-gray-500" />
                        <p><EMAIL></p>
                      </div>
                      <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400 text-xs">
                        <FileText className="h-3 w-3 flex-shrink-0 text-gray-400 dark:text-gray-500" />
                        <p>TIN: C0000987654</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Invoice Details */}
                <div className="mb-6">
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-100 dark:border-gray-700">
                    <h3 className="font-bold text-gray-700 dark:text-white mb-2 flex items-center gap-2 text-sm uppercase tracking-wider">
                      <FileText className="h-4 w-4 text-ghana-green dark:text-ghana-gold" />
                      <span>Invoice Details</span>
                    </h3>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Date:</span>
                        <span className="font-medium dark:text-gray-300">Dec 15, 2024</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Due:</span>
                        <span className="font-medium dark:text-gray-300">Jan 14, 2025</span>
                      </div>
                    </div>
                  </div>
                </div>
            {/* Invoice Items */}
                <div className="mb-6">
                  <h3 className="font-bold text-gray-700 dark:text-white mb-3 flex items-center gap-2 text-sm uppercase tracking-wider">
                    <FileText className="h-4 w-4 text-ghana-green dark:text-ghana-gold" />
                    <span>Invoice Items</span>
                  </h3>

                  <div className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
                    <table className="w-full text-xs">
                      <thead>
                        <tr className="bg-gray-50 dark:bg-ghana-green">
                          <th className="text-left py-2 px-3 font-bold text-gray-600 dark:text-white uppercase tracking-wider">Description</th>
                          <th className="text-right py-2 px-3 font-bold text-gray-600 dark:text-white uppercase tracking-wider">Qty</th>
                          <th className="text-right py-2 px-3 font-bold text-gray-600 dark:text-white uppercase tracking-wider">Price</th>
                          <th className="text-right py-2 px-3 font-bold text-gray-600 dark:text-white uppercase tracking-wider">Amount</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-t border-gray-100 dark:border-gray-700">
                          <td className="py-2 px-3 font-medium dark:text-gray-200">Web Development</td>
                          <td className="py-2 px-3 text-right dark:text-gray-300">1</td>
                          <td className="py-2 px-3 text-right dark:text-gray-300">2,500.00 GHS</td>
                          <td className="py-2 px-3 text-right font-medium dark:text-gray-200">2,500.00 GHS</td>
                        </tr>
                        <tr className="border-t border-gray-100 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800">
                          <td className="py-2 px-3 font-medium dark:text-gray-200">UI/UX Design</td>
                          <td className="py-2 px-3 text-right dark:text-gray-300">1</td>
                          <td className="py-2 px-3 text-right dark:text-gray-300">1,800.00 GHS</td>
                          <td className="py-2 px-3 text-right font-medium dark:text-gray-200">1,800.00 GHS</td>
                        </tr>
                        <tr className="border-t border-gray-100 dark:border-gray-700">
                          <td className="py-2 px-3 font-medium dark:text-gray-200">Consultation</td>
                          <td className="py-2 px-3 text-right dark:text-gray-300">2</td>
                          <td className="py-2 px-3 text-right dark:text-gray-300">450.00 GHS</td>
                          <td className="py-2 px-3 text-right font-medium dark:text-gray-200">900.00 GHS</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Invoice Summary */}
                <div className="flex justify-end mb-6">
                  <div className="w-full md:w-64 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <div className="bg-gray-50 dark:bg-ghana-green py-2 px-3 border-b border-gray-200 dark:border-gray-700">
                      <h3 className="font-bold text-gray-700 dark:text-white text-sm uppercase tracking-wider">Invoice Summary</h3>
                    </div>
                    <div className="p-3 space-y-2 dark:bg-gray-900 text-xs">
                      <div className="flex justify-between py-1 border-b border-gray-100 dark:border-gray-700">
                        <span className="text-gray-600 dark:text-gray-400">Subtotal:</span>
                        <span className="font-medium dark:text-gray-300">5,200.00 GHS</span>
                      </div>
                      <div className="flex justify-between py-1 border-b border-gray-100 dark:border-gray-700">
                        <span className="text-gray-600 dark:text-gray-400">VAT (15%):</span>
                        <span className="font-medium dark:text-gray-300">780.00 GHS</span>
                      </div>
                      <div className="flex justify-between py-1 border-b border-gray-100 dark:border-gray-700">
                        <span className="text-gray-600 dark:text-gray-400">Levies:</span>
                        <span className="font-medium dark:text-gray-300">260.00 GHS</span>
                      </div>
                      <div className="flex justify-between py-2 mt-2 bg-ghana-green/10 dark:bg-ghana-green/20 rounded-lg px-2">
                        <span className="font-bold text-ghana-green dark:text-ghana-gold">Total:</span>
                        <span className="font-bold text-ghana-green dark:text-ghana-gold">6,240.00 GHS</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Footer */}
                <div className="text-center border-t border-gray-200 dark:border-gray-700 pt-4 pb-2">
                  <div className="flex items-center justify-center gap-2 mb-1">
                    <Shield className="h-4 w-4 text-ghana-green dark:text-ghana-gold" />
                    <p className="text-ghana-green dark:text-ghana-gold font-medium text-xs">GRA Compliant Invoice</p>
                  </div>
                  <p className="text-gray-500 dark:text-gray-300 text-xs">Thank you for your business!</p>
                  <p className="text-gray-400 dark:text-gray-500 text-xs mt-1">Generated by Payvoicer</p>
                </div>
              </div>
            </div>

            {/* Scroll indicator */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-12 animate-bounce hidden md:block">
              <ChevronDown className="h-6 w-6 text-ghana-green dark:text-ghana-gold" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
