import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Download, X, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

/**
 * InstallApp component - DISABLED
 *
 * This component has been disabled to prevent button duplication.
 * UniversalInstallButton now handles all PWA installation functionality.
 */
const InstallApp: React.FC = () => {
  // Component disabled to prevent duplication with UniversalInstallButton
  return null;

  useEffect(() => {
    // Check if already installed
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setInstalled(true);
      return;
    }

    // Check if user has dismissed the prompt
    const hasUserDismissed = localStorage.getItem('pwa-prompt-dismissed');
    const dismissedTime = parseInt(localStorage.getItem('pwa-prompt-dismissed-time') || '0', 10);
    const now = Date.now();
    
    // If dismissed less than 7 days ago, don't show
    if (hasUserDismissed === 'true' && now - dismissedTime < 7 * 24 * 60 * 60 * 1000) {
      return;
    }

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault();
      // Stash the event so it can be triggered later
      setDeferredPrompt(e);
      setInstallable(true);
      
      // Show the prompt after a short delay
      setTimeout(() => {
        setShowPrompt(true);
      }, 3000);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt as any);

    // Listen for app installed event
    const handleAppInstalled = () => {
      setInstalled(true);
      setShowPrompt(false);
      localStorage.setItem('pwa-installed', 'true');
    };

    window.addEventListener('appinstalled', handleAppInstalled);

    // Check if the app was previously installed
    if (localStorage.getItem('pwa-installed') === 'true') {
      setInstalled(true);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt as any);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const handleInstall = async () => {
    if (!deferredPrompt) {
      return;
    }

    // Show the install prompt
    deferredPrompt.prompt();

    // Wait for the user to respond to the prompt
    const { outcome } = await deferredPrompt.userChoice;
    console.log(`User response to the install prompt: ${outcome}`);

    // We've used the prompt, and can't use it again, discard it
    setDeferredPrompt(null);
    setShowPrompt(false);

    if (outcome === 'accepted') {
      setInstalled(true);
      localStorage.setItem('pwa-installed', 'true');
    }
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    localStorage.setItem('pwa-prompt-dismissed', 'true');
    localStorage.setItem('pwa-prompt-dismissed-time', Date.now().toString());
  };

  if (installed || !installable || !showPrompt) {
    return null;
  }

  return (
    <AnimatePresence>
      {showPrompt && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.3 }}
          className="fixed bottom-4 left-4 right-4 z-50 max-w-md mx-auto"
        >
          <Card className="overflow-hidden shadow-xl border-ghana-green/10 dark:border-ghana-gold/10">
            <div className="bg-gradient-to-r from-ghana-green to-ghana-green/80 dark:from-ghana-gold dark:to-ghana-gold/80 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-white/20 p-2 rounded-full">
                    <Download className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="font-bold text-white text-lg">Install Payvoicer</h3>
                </div>
                <button
                  onClick={handleDismiss}
                  className="text-white/70 hover:text-white transition-colors"
                  aria-label="Dismiss"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>
            
            <div className="p-4 bg-white dark:bg-gray-800">
              <div className="space-y-3">
                <p className="text-gray-700 dark:text-gray-200">
                  Install Payvoicer on your device for a better experience:
                </p>
                
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                  <li className="flex items-start gap-2">
                    <ChevronRight className="h-4 w-4 text-ghana-green dark:text-ghana-gold mt-0.5 flex-shrink-0" />
                    <span>Access invoices even when offline</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <ChevronRight className="h-4 w-4 text-ghana-green dark:text-ghana-gold mt-0.5 flex-shrink-0" />
                    <span>Faster loading and better performance</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <ChevronRight className="h-4 w-4 text-ghana-green dark:text-ghana-gold mt-0.5 flex-shrink-0" />
                    <span>Launch directly from your home screen</span>
                  </li>
                </ul>
                
                <div className="flex justify-end space-x-3 mt-4">
                  <Button
                    variant="outline"
                    onClick={handleDismiss}
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700/50"
                  >
                    Not Now
                  </Button>
                  <Button
                    onClick={handleInstall}
                    className="bg-ghana-green hover:bg-ghana-green/90 text-white dark:bg-ghana-gold dark:hover:bg-ghana-gold/90 dark:text-gray-900"
                  >
                    Install App
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default InstallApp;
