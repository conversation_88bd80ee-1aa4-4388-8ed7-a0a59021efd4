import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Download, X, ChevronRight, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
// Import CSS directly
import './pwa-install.css';

type Platform = 'ios-safari' | 'ios-chrome' | 'android' | 'desktop' | 'other';

// BeforeInstallPromptEvent interface
interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

// PWA Manager to coordinate all PWA components
class PWAManager {
  private static instance: PWAManager;
  private activeComponent: string | null = null;
  private listeners: Set<(component: string | null) => void> = new Set();

  static getInstance(): PWAManager {
    if (!PWAManager.instance) {
      PWAManager.instance = new PWAManager();
    }
    return PWAManager.instance;
  }

  setActiveComponent(component: string | null) {
    this.activeComponent = component;
    this.listeners.forEach(listener => listener(component));
  }

  getActiveComponent(): string | null {
    return this.activeComponent;
  }

  subscribe(listener: (component: string | null) => void) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  isComponentActive(component: string): boolean {
    return this.activeComponent === component || this.activeComponent === null;
  }
}

const PWAInstallPrompt: React.FC = () => {
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showButton, setShowButton] = useState<boolean>(false);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [platform, setPlatform] = useState<Platform>('other');
  const [isInstalled, setIsInstalled] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [isActiveComponent, setIsActiveComponent] = useState<boolean>(true);

  // PWA Manager integration
  const pwaManager = PWAManager.getInstance();

  useEffect(() => {
    // Subscribe to PWA manager
    const unsubscribe = pwaManager.subscribe((activeComponent) => {
      setIsActiveComponent(activeComponent === 'PWAInstallPrompt' || activeComponent === null);
    });

    return unsubscribe;
  }, [pwaManager]);

  useEffect(() => {
    try {
      // Check if already installed
      if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true);
        return;
      }

      // Check if user has dismissed the prompt recently
      const dismissedTime = parseInt(localStorage.getItem('pwa-prompt-dismissed-time') || '0', 10);
      const now = Date.now();

      // If dismissed less than 3 days ago, don't show
      if (now - dismissedTime < 3 * 24 * 60 * 60 * 1000) {
        return;
      }

      // Detect platform safely
      let detectedPlatform: Platform = 'other';
      try {
        const userAgent = navigator.userAgent || navigator.vendor || '';

        // Check if device is iOS
        const isIOSDevice = /iPad|iPhone|iPod/i.test(userAgent) && !window.MSStream;

        if (isIOSDevice) {
          // Check if browser is Chrome on iOS
          if (/CriOS/i.test(userAgent)) {
            detectedPlatform = 'ios-chrome';
          } else {
            // Default iOS browser (Safari)
            detectedPlatform = 'ios-safari';
          }

          // For iOS, this component should NOT show - let UniversalInstallButton handle it
          console.log('iOS device detected, PWAInstallPrompt will not show');
          return;
        } else if (/Android/i.test(userAgent)) {
          detectedPlatform = 'android';
        } else if (!/Mobile|Android|iP(ad|hone|od)/i.test(userAgent)) {
          detectedPlatform = 'desktop';
        }
        setPlatform(detectedPlatform);

        // Set this component as active for non-iOS platforms
        pwaManager.setActiveComponent('PWAInstallPrompt');
      } catch (e) {
        console.warn('Error detecting platform:', e);
        // Default to 'other' if platform detection fails
        setPlatform('other');
      }

      // Listen for beforeinstallprompt event (Chrome, Edge, etc.)
      const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
        try {
          // Prevent Chrome 67 and earlier from automatically showing the prompt
          e.preventDefault();
          // Stash the event so it can be triggered later
          setInstallPrompt(e);

          // Show the install button after a short delay
          setTimeout(() => {
            setShowButton(true);
          }, 3000);
        } catch (err) {
          console.error('Error in beforeinstallprompt handler:', err);
        }
      };

      // Only add event listener if it's supported
      if (window.addEventListener) {
        window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      }

      // Listen for app installed event
      const handleAppInstalled = () => {
        try {
          setIsInstalled(true);
          setShowButton(false);
          setShowModal(false);
          localStorage.setItem('pwa-installed', 'true');
        } catch (err) {
          console.error('Error in appinstalled handler:', err);
        }
      };

      // Only add event listener if it's supported
      if (window.addEventListener) {
        window.addEventListener('appinstalled', handleAppInstalled);
      }

      return () => {
        // Only remove event listeners if they were added
        if (window.removeEventListener) {
          window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
          window.removeEventListener('appinstalled', handleAppInstalled);
        }
      };
    } catch (err) {
      console.error('Error in PWAInstallPrompt useEffect:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      return () => {}; // Return empty cleanup function
    }
  }, [pwaManager]);

  const handleInstall = async () => {
    try {
      // Always show the modal first for all platforms
      console.log('Install button clicked, showing modal');
      setShowModal(true);

      // Check for iOS devices using multiple detection methods
      const isIOS =
        platform === 'ios-safari' ||
        platform === 'ios-chrome' ||
        /iPad|iPhone|iPod/i.test(navigator.userAgent) ||
        (navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)) ||
        (navigator.userAgent.includes("Mac") && "ontouchend" in document);

      if (isIOS) {
        console.log('iOS device detected, showing iOS-specific guide');
        // Modal is already showing, no need to set it again
        return;
      }

      // If we have an install prompt, we can also try to use it
      if (installPrompt) {
        try {
          console.log('Native install prompt available, showing it');
          // Show the install prompt
          await installPrompt.prompt();

          // Wait for the user to respond to the prompt
          const { outcome } = await installPrompt.userChoice;
          console.log(`User response to the install prompt: ${outcome}`);

          // Clear the saved prompt
          setInstallPrompt(null);

          if (outcome === 'accepted') {
            setIsInstalled(true);
            setShowButton(false);
            localStorage.setItem('pwa-installed', 'true');
            // Close the modal if installation was successful
            setShowModal(false);
          }
        } catch (error) {
          console.error('Error during installation:', error);
          // Modal is already showing, so no need to set it again
        }
      } else {
        console.log('No native install prompt available, showing modal only');
      }
    } catch (err) {
      console.error('Error in handleInstall:', err);
      // Fallback to showing the modal with instructions
      setShowModal(true);
    }
  };

  const handleDismiss = () => {
    try {
      setShowButton(false);
      setShowModal(false);
      localStorage.setItem('pwa-prompt-dismissed-time', Date.now().toString());
    } catch (err) {
      console.error('Error in handleDismiss:', err);
      // Just hide the UI elements if localStorage fails
      setShowButton(false);
      setShowModal(false);
    }
  };

  // Don't render anything if there's an error, app is installed, or not the active component
  if (error || isInstalled || !isActiveComponent) {
    return null;
  }

  // Safely check if we're in a browser environment
  if (typeof window === 'undefined') {
    return null;
  }

  // Check if this is an iOS device
  const isIOSDevice =
    platform === 'ios-safari' ||
    platform === 'ios-chrome' ||
    /iPad|iPhone|iPod/i.test(navigator.userAgent) ||
    (navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)) ||
    (navigator.userAgent.includes("Mac") && "ontouchend" in document);

  // Don't render on iOS devices (UniversalInstallButton handles iOS)
  if (isIOSDevice) {
    return null;
  }

  // Don't render if no button should be shown
  if (!showButton) {
    return null;
  }

  try {
    return (
      <>
        {/* Sticky Install Button */}
        <div className="pwa-install-container">
          {showButton && (
            <div
              className="pwa-install-button"
              style={{
                position: 'fixed',
                bottom: '1.5rem',
                right: '1.5rem',
                zIndex: 99999
              }}
            >
              <Button
                onClick={handleInstall}
                className="bg-ghana-green hover:bg-ghana-green/90 text-white dark:bg-ghana-gold dark:hover:bg-ghana-gold/90 dark:text-gray-900 rounded-full shadow-lg pwa-install-fab"
                aria-label="Install Payvoicer App"
              >
                <Download className="h-5 w-5 mr-2" />
                <span>Install</span>
              </Button>
            </div>
          )}
        </div>

      {/* Installation Instructions Modal */}
      {/* Use a custom modal implementation for iOS compatibility */}
      {showModal && (
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-[999999] p-4"
          style={{
            WebkitBackdropFilter: 'blur(4px)',
            backdropFilter: 'blur(4px)'
          }}
          onClick={() => setShowModal(false)}
        >
          <div
            className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden max-w-md w-full"
            style={{
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
              maxWidth: '95vw',
              width: '450px'
            }}
            onClick={(e) => e.stopPropagation()}
          >
          <div className="pwa-install-modal-header">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-white/20 p-2 rounded-full">
                  <img
                    src="/logo.png"
                    alt="Payvoicer Logo"
                    className="h-6 w-6 object-contain"
                    onError={(e) => {
                      // Fallback to icon if logo doesn't load
                      const target = e.target as HTMLImageElement;
                      target.onerror = null;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        const icon = document.createElement('span');
                        icon.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-white dark:text-gray-900"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg>';
                        parent.appendChild(icon);
                      }
                    }}
                  />
                </div>
                <h3 className="font-bold text-white dark:text-gray-900 text-lg">Install Payvoicer</h3>
              </div>
              <button
                onClick={handleDismiss}
                className="text-white/70 hover:text-white dark:text-gray-900/70 dark:hover:text-gray-900 transition-colors"
                aria-label="Close"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="p-6">
            {/* This component now only handles Android, Desktop, and Other platforms */}

            {platform === 'android' && (
              <div className="space-y-6 pwa-install-android">
                <p className="text-gray-700 dark:text-gray-300 text-center font-medium text-lg">
                  Install Payvoicer on your Android device
                </p>
                <p className="text-gray-500 dark:text-gray-400 text-center text-sm -mt-4">
                  Follow these simple steps to install the app:
                </p>

                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center mb-6">
                    <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0">
                      <span className="text-blue-600 dark:text-blue-300 font-bold text-lg">1</span>
                    </div>
                    <div className="ml-4">
                      <p className="font-medium text-base">
                        Tap the Menu button
                        <Menu className="h-5 w-5 text-blue-500 inline-block ml-2 align-text-bottom" />
                      </p>
                      <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
                        Located at the top-right of your browser
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0">
                      <span className="text-blue-600 dark:text-blue-300 font-bold text-lg">2</span>
                    </div>
                    <div className="ml-4">
                      <p className="font-medium text-base">
                        Tap "Install app" or "Add to Home screen"
                        <Download className="h-5 w-5 text-blue-500 inline-block ml-2 align-text-bottom" />
                      </p>
                      <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
                        Look for the install option in the menu
                      </p>
                    </div>
                  </div>
                </div>

                <div className="pt-6 flex justify-center">
                  <Button
                    onClick={handleDismiss}
                    className="bg-ghana-green hover:bg-ghana-green/90 text-white dark:bg-ghana-gold dark:hover:bg-ghana-gold/90 dark:text-gray-900 px-8 py-2 text-sm font-medium rounded-lg shadow-md hover:shadow-lg transition-all"
                  >
                    Got it, thanks!
                  </Button>
                </div>
              </div>
            )}

            {platform === 'desktop' && (
              <div className="space-y-6 pwa-install-desktop">
                <p className="text-gray-700 dark:text-gray-300 text-center font-medium text-lg">
                  Install Payvoicer on your computer
                </p>
                <p className="text-gray-500 dark:text-gray-400 text-center text-sm -mt-4">
                  Get the desktop app for better performance:
                </p>

                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center mb-6">
                    <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0">
                      <span className="text-blue-600 dark:text-blue-300 font-bold text-lg">1</span>
                    </div>
                    <div className="ml-4">
                      <p className="font-medium text-base">
                        Click the install icon in the address bar
                        <Download className="h-5 w-5 text-blue-500 inline-block ml-2 align-text-bottom" />
                      </p>
                      <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
                        Look for the install icon at the right side of the address bar
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="bg-blue-100 dark:bg-blue-900 rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0">
                      <span className="text-blue-600 dark:text-blue-300 font-bold text-lg">2</span>
                    </div>
                    <div className="ml-4">
                      <p className="font-medium text-base">
                        Click "Install" in the popup
                      </p>
                      <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
                        Follow the installation prompts to complete setup
                      </p>
                    </div>
                  </div>
                </div>

                <div className="pt-6 flex justify-center space-x-3">
                  <Button
                    variant="outline"
                    onClick={handleDismiss}
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700/50 px-6 py-2 text-sm"
                  >
                    Maybe Later
                  </Button>
                  <Button
                    onClick={handleInstall}
                    className="bg-ghana-green hover:bg-ghana-green/90 text-white dark:bg-ghana-gold dark:hover:bg-ghana-gold/90 dark:text-gray-900 px-6 py-2 text-sm font-medium rounded-lg shadow-md hover:shadow-lg transition-all"
                  >
                    Install Now
                  </Button>
                </div>
              </div>
            )}

            {platform === 'other' && (
              <div className="space-y-6">
                <p className="text-gray-700 dark:text-gray-300 text-center font-medium text-lg">
                  Install Payvoicer App
                </p>
                <p className="text-gray-500 dark:text-gray-400 text-center text-sm -mt-4">
                  Get offline access and a better experience:
                </p>

                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-100 dark:border-gray-700">
                  <ul className="space-y-4">
                    <li className="flex items-start gap-3">
                      <div className="bg-ghana-green/10 dark:bg-ghana-gold/10 rounded-full p-1 mt-0.5">
                        <ChevronRight className="h-3 w-3 text-ghana-green dark:text-ghana-gold" />
                      </div>
                      <span className="text-gray-700 dark:text-gray-300 text-sm">Access invoices even when offline</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="bg-ghana-green/10 dark:bg-ghana-gold/10 rounded-full p-1 mt-0.5">
                        <ChevronRight className="h-3 w-3 text-ghana-green dark:text-ghana-gold" />
                      </div>
                      <span className="text-gray-700 dark:text-gray-300 text-sm">Faster loading and better performance</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="bg-ghana-green/10 dark:bg-ghana-gold/10 rounded-full p-1 mt-0.5">
                        <ChevronRight className="h-3 w-3 text-ghana-green dark:text-ghana-gold" />
                      </div>
                      <span className="text-gray-700 dark:text-gray-300 text-sm">Launch directly from your home screen</span>
                    </li>
                  </ul>
                </div>

                <div className="pt-6 flex justify-center space-x-3">
                  <Button
                    variant="outline"
                    onClick={handleDismiss}
                    className="border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700/50 px-6 py-2 text-sm"
                  >
                    Maybe Later
                  </Button>
                  <Button
                    onClick={handleInstall}
                    className="bg-ghana-green hover:bg-ghana-green/90 text-white dark:bg-ghana-gold dark:hover:bg-ghana-gold/90 dark:text-gray-900 px-6 py-2 text-sm font-medium rounded-lg shadow-md hover:shadow-lg transition-all"
                  >
                    Install Now
                  </Button>
                </div>
              </div>
            )}
          </div>
          </div>
        </div>
      )}
    </>
  );
  } catch (err) {
    console.error('Error rendering PWAInstallPrompt:', err);
    return null;
  }
};

export default PWAInstallPrompt;
