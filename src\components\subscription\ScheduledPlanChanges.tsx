import React, { useState, useEffect } from 'react';
import { Calendar, Clock, CreditCard, X, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from '@/hooks/use-toast';
import { useSession } from '@/contexts/SessionContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { getScheduledPlanChanges, cancelScheduledPlanChange } from '@/services/enhancedSubscriptionService';

interface ScheduledPlanChange {
  id: string;
  current_plan_type: string;
  current_billing_cycle: string;
  new_plan_type: string;
  new_billing_cycle: string;
  scheduled_date: string;
  amount_paid: number;
  payment_reference: string;
  status: string;
  created_at: string;
}

const ScheduledPlanChanges: React.FC = () => {
  const { user } = useSession();
  const { refreshSubscription } = useSubscription();
  const [scheduledChanges, setScheduledChanges] = useState<ScheduledPlanChange[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCancelling, setIsCancelling] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadScheduledChanges();
    }
  }, [user]);

  const loadScheduledChanges = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const changes = await getScheduledPlanChanges(user.id);
      setScheduledChanges(changes);
    } catch (error) {
      console.error('Error loading scheduled plan changes:', error);
      toast({
        title: 'Error',
        description: 'Failed to load scheduled plan changes.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelChange = async (changeId: string) => {
    if (!user) return;

    setIsCancelling(changeId);
    try {
      const result = await cancelScheduledPlanChange(user.id, changeId);
      
      if (result.success) {
        toast({
          title: 'Plan Change Cancelled',
          description: 'Your scheduled plan change has been cancelled successfully.',
        });
        
        // Refresh the list and subscription context
        await loadScheduledChanges();
        await refreshSubscription();
      } else {
        throw new Error(result.error || 'Failed to cancel plan change');
      }
    } catch (error) {
      console.error('Error cancelling plan change:', error);
      toast({
        title: 'Error',
        description: 'Failed to cancel the scheduled plan change. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsCancelling(null);
    }
  };

  const formatPlanName = (plan: string) => {
    return plan.charAt(0).toUpperCase() + plan.slice(1);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return `GHS ${amount.toFixed(2)}`;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Scheduled Plan Changes
          </CardTitle>
          <CardDescription>
            Loading scheduled plan changes...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (scheduledChanges.length === 0) {
    return null; // Don't show the component if there are no scheduled changes
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Scheduled Plan Changes
        </CardTitle>
        <CardDescription>
          You have {scheduledChanges.length} scheduled plan change{scheduledChanges.length !== 1 ? 's' : ''}.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Your plan changes are scheduled to take effect at the end of your current billing period. 
            Payment has already been processed.
          </AlertDescription>
        </Alert>

        {scheduledChanges.map((change) => (
          <div key={change.id} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  Scheduled
                </Badge>
                <span className="text-sm text-muted-foreground">
                  Payment Reference: {change.payment_reference}
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleCancelChange(change.id)}
                disabled={isCancelling === change.id}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                {isCancelling === change.id ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                ) : (
                  <X className="h-4 w-4" />
                )}
                Cancel
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Current Plan</h4>
                <div className="text-sm">
                  <div>{formatPlanName(change.current_plan_type)}</div>
                  <div className="text-muted-foreground">
                    {change.current_billing_cycle}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-sm">New Plan</h4>
                <div className="text-sm">
                  <div className="font-medium text-green-600">
                    {formatPlanName(change.new_plan_type)}
                  </div>
                  <div className="text-muted-foreground">
                    {change.new_billing_cycle}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-sm">Effective Date</h4>
                <div className="text-sm">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {formatDate(change.scheduled_date)}
                  </div>
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <CreditCard className="h-3 w-3" />
                    {formatCurrency(change.amount_paid)} paid
                  </div>
                </div>
              </div>
            </div>

            <div className="text-xs text-muted-foreground pt-2 border-t">
              Scheduled on {formatDate(change.created_at)}
            </div>
          </div>
        ))}

        <div className="text-sm text-muted-foreground">
          <p>
            <strong>Note:</strong> Cancelling a scheduled plan change will not refund the payment. 
            Your current plan will continue until its natural expiry date.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default ScheduledPlanChanges;
