import { useEffect, useState } from 'react';
import {
  Users,
  CreditCard,
  FileText,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  UserPlus,
  FileUp,
  Newspaper,
  GanttChart,
  ScrollText,
  RefreshCw
} from 'lucide-react';
import { useSession } from '@/contexts/SessionContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { supabase } from '@/integrations/supabase/client';
import { isAdmin as checkIsAdmin, isAdminEmail } from '@/utils/adminUtils';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AdminLayout from '@/components/layouts/AdminLayout';

// Dashboard stat card component
interface StatCardProps {
  title: string;
  value: string;
  description: string;
  icon: React.ReactNode;
  trend?: 'up' | 'down';
  trendValue?: string;
}

// Recent activity interface
interface RecentActivity {
  id: string;
  type: string;
  description: string;
  user: string;
  client: string;
  amount: number;
  status: string;
  timestamp: string;
}

const StatCard = ({ title, value, description, icon, trend, trendValue }: StatCardProps) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      <div className="h-4 w-4 text-muted-foreground">{icon}</div>
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      <p className="text-xs text-muted-foreground">{description}</p>
      {trend && trendValue && (
        <div className={`flex items-center text-xs ${trend === 'up' ? 'text-green-500' : 'text-red-500'} mt-2`}>
          {trend === 'up' ? <ArrowUpRight className="mr-1 h-3 w-3" /> : <ArrowDownRight className="mr-1 h-3 w-3" />}
          <span>{trendValue} from last month</span>
        </div>
      )}
    </CardContent>
  </Card>
);

const AdminDashboard = () => {
  const { session, user } = useSession();
  const { subscription } = useSubscription();
  const [isLoading, setIsLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<{
    totalUsers: number;
    activeUsers: number;
    totalInvoices: number;
    totalRevenue: number;
    newUsers: number;
    paidInvoices: number;
    activeSubscriptions: number;
    totalClients: number;
    overdueInvoices: number;
    draftInvoices: number;
    sentInvoices: number;
    monthlyRevenue: number;
    averageInvoiceValue: number;
    paymentIntegrations: number;
    recentActivity: RecentActivity[];
    blogPosts: number;
    faqs: number;
    helpArticles: number;
    communityDiscussions: number;
    graCredentials: number;
    paystackIntegrations: number;
    emailNotifications: number;
  }>({
    totalUsers: 0,
    activeUsers: 0,
    totalInvoices: 0,
    totalRevenue: 0,
    newUsers: 0,
    paidInvoices: 0,
    activeSubscriptions: 0,
    totalClients: 0,
    overdueInvoices: 0,
    draftInvoices: 0,
    sentInvoices: 0,
    monthlyRevenue: 0,
    averageInvoiceValue: 0,
    paymentIntegrations: 0,
    recentActivity: [],
    blogPosts: 0,
    faqs: 0,
    helpArticles: 0,
    communityDiscussions: 0,
    graCredentials: 0,
    paystackIntegrations: 0,
    emailNotifications: 0
  });

  useEffect(() => {
    const checkAdminAndFetchStats = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (!session || !user) {
          setIsLoading(false);
          setError('You must be logged in to access the admin dashboard.');
          return;
        }

        const userId = session.user.id;

        // Get user email directly from the session user object
        // This avoids an extra database call that might fail
        const userEmail = user.email || '';

        // Check if user is admin (either by email or role)
        try {
          const adminStatus = await checkIsAdmin(userId, userEmail);

          if (adminStatus) {
            setIsAdmin(true);
            fetchDashboardStats();
          } else {
            setIsAdmin(false);
            setError('You do not have permission to access the admin dashboard.');
          }
        } catch (adminError) {
          console.error('Error checking admin status:', adminError);

          // Fallback to just checking email if there's an error with the full check
          if (isAdminEmail(userEmail)) {
            setIsAdmin(true);
            fetchDashboardStats();
          } else {
            setIsAdmin(false);
            setError('You do not have permission to access the admin dashboard.');
          }
        }
      } catch (error) {
        console.error('Error in admin verification:', error);
        setIsAdmin(false);
        setError('An error occurred while verifying admin permissions.');
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminAndFetchStats();
  }, [session, user]);

  const fetchDashboardStats = async () => {
    try {
      // Initialize stats with default values
      let statsData = {
        totalUsers: 0,
        activeUsers: 0,
        totalInvoices: 0,
        totalRevenue: 0,
        newUsers: 0,
        paidInvoices: 0,
        activeSubscriptions: 0,
        totalClients: 0,
        overdueInvoices: 0,
        draftInvoices: 0,
        sentInvoices: 0,
        monthlyRevenue: 0,
        averageInvoiceValue: 0,
        paymentIntegrations: 0,
        recentActivity: [],
        blogPosts: 0,
        faqs: 0,
        helpArticles: 0,
        communityDiscussions: 0,
        graCredentials: 0,
        paystackIntegrations: 0,
        emailNotifications: 0
      };

      // Fetch total users from auth.users table
      try {
        const { count: totalUsers, error: usersError } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true });

        if (!usersError) {
          statsData.totalUsers = totalUsers || 0;
        } else {
          console.error('Error fetching users:', usersError);
          // Fallback to profiles table if auth.users is not accessible
          const { count: profileCount, error: profileError } = await supabase
            .from('profiles')
            .select('*', { count: 'exact', head: true });

          if (!profileError) {
            statsData.totalUsers = profileCount || 0;
          }
        }
      } catch (error) {
        console.error('Error fetching users:', error);
      }

      // Fetch total invoices
      try {
        const { count: totalInvoices, error: invoicesError } = await supabase
          .from('invoices')
          .select('*', { count: 'exact', head: true });

        if (!invoicesError) {
          statsData.totalInvoices = totalInvoices || 0;
        } else {
          console.error('Error fetching invoices:', invoicesError);
        }
      } catch (error) {
        console.error('Error fetching invoices:', error);
      }

      // Fetch paid invoices
      try {
        const { count: paidInvoices, error: paidInvoicesError } = await supabase
          .from('invoices')
          .select('*', { count: 'exact', head: true })
          .eq('status', 'paid');

        if (!paidInvoicesError) {
          statsData.paidInvoices = paidInvoices || 0;
        } else {
          console.error('Error fetching paid invoices:', paidInvoicesError);
        }
      } catch (error) {
        console.error('Error fetching paid invoices:', error);
      }

      // Fetch new users in the last 30 days from auth.users
      try {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const { count: newUsers, error: newUsersError } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true })
          .gte('created_at', thirtyDaysAgo.toISOString());

        if (!newUsersError) {
          statsData.newUsers = newUsers || 0;
        } else {
          console.error('Error fetching new users:', newUsersError);
          // Fallback to profiles table
          const { count: profileNewUsers, error: profileError } = await supabase
            .from('profiles')
            .select('*', { count: 'exact', head: true })
            .gte('created_at', thirtyDaysAgo.toISOString());

          if (!profileError) {
            statsData.newUsers = profileNewUsers || 0;
          }
        }
      } catch (error) {
        console.error('Error fetching new users:', error);
      }

      // Fetch active users (users who have created at least one invoice)
      try {
        const { data: invoiceUsers, error: invoiceUsersError } = await supabase
          .from('invoices')
          .select('user_id')
          .is('user_id', 'not.null');

        if (!invoiceUsersError && invoiceUsers) {
          // Get unique user IDs
          const uniqueUserIds = [...new Set(invoiceUsers.map(i => i.user_id))];
          statsData.activeUsers = uniqueUserIds.length;
        } else {
          console.error('Error fetching invoice users:', invoiceUsersError);
        }
      } catch (error) {
        console.error('Error fetching active users:', error);
      }

      // Calculate total revenue from invoice_payments table
      try {
        const { data: paymentData, error: paymentError } = await supabase
          .from('invoice_payments')
          .select('amount');

        if (!paymentError && paymentData) {
          statsData.totalRevenue = paymentData.reduce((sum, payment) => sum + (payment.amount || 0), 0);
        } else {
          // Fallback to invoices table if invoice_payments has an error
          const { data: invoiceData, error: revenueError } = await supabase
            .from('invoices')
            .select('total_amount')
            .eq('status', 'paid');

          if (!revenueError && invoiceData) {
            statsData.totalRevenue = invoiceData.reduce((sum, invoice) => sum + (invoice.total_amount || 0), 0);
          } else {
            console.error('Error fetching revenue data:', revenueError);
          }
        }
      } catch (error) {
        console.error('Error calculating total revenue:', error);
      }

      // Fetch subscription data
      try {
        const { data: subscriptionData, error: subscriptionError } = await supabase
          .from('subscriptions')
          .select('*');

        if (!subscriptionError && subscriptionData) {
          // Add subscription stats to the dashboard
          const activeSubscriptions = subscriptionData.filter(sub =>
            sub.status === 'active' || sub.status === 'trialing'
          ).length;

          // Add to the stats object
          statsData = {
            ...statsData,
            activeSubscriptions: activeSubscriptions || 0,
          };
        }
      } catch (error) {
        console.error('Error fetching subscription data:', error);
      }

      // Fetch total clients
      try {
        const { count: totalClients, error: clientsError } = await supabase
          .from('clients')
          .select('*', { count: 'exact', head: true });

        if (!clientsError) {
          statsData.totalClients = totalClients || 0;
        }
      } catch (error) {
        console.error('Error fetching clients:', error);
      }

      // Fetch invoice status breakdown
      try {
        const { data: invoiceStatusData, error: statusError } = await supabase
          .from('invoices')
          .select('status');

        if (!statusError && invoiceStatusData) {
          const statusCounts = invoiceStatusData.reduce((acc, invoice) => {
            acc[invoice.status] = (acc[invoice.status] || 0) + 1;
            return acc;
          }, {} as Record<string, number>);

          statsData.overdueInvoices = statusCounts.overdue || 0;
          statsData.draftInvoices = statusCounts.draft || 0;
          statsData.sentInvoices = statusCounts.sent || 0;
        }
      } catch (error) {
        console.error('Error fetching invoice status data:', error);
      }

      // Fetch monthly revenue (current month)
      try {
        const currentMonth = new Date();
        const firstDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);

        const { data: monthlyPayments, error: monthlyError } = await supabase
          .from('invoice_payments')
          .select('amount')
          .eq('status', 'successful')
          .gte('payment_date', firstDayOfMonth.toISOString());

        if (!monthlyError && monthlyPayments) {
          statsData.monthlyRevenue = monthlyPayments.reduce((sum, payment) => sum + (payment.amount || 0), 0);
        }
      } catch (error) {
        console.error('Error fetching monthly revenue:', error);
      }

      // Calculate average invoice value
      try {
        const { data: invoiceValues, error: avgError } = await supabase
          .from('invoices')
          .select('total_amount')
          .neq('total_amount', 0);

        if (!avgError && invoiceValues && invoiceValues.length > 0) {
          const totalValue = invoiceValues.reduce((sum, invoice) => sum + (invoice.total_amount || 0), 0);
          statsData.averageInvoiceValue = totalValue / invoiceValues.length;
        }
      } catch (error) {
        console.error('Error calculating average invoice value:', error);
      }

      // Fetch payment integrations count
      try {
        const { count: integrationCount, error: integrationError } = await supabase
          .from('payment_integrations')
          .select('*', { count: 'exact', head: true })
          .eq('is_enabled', true);

        if (!integrationError) {
          statsData.paymentIntegrations = integrationCount || 0;
        }
      } catch (error) {
        console.error('Error fetching payment integrations:', error);
      }

      // Fetch additional comprehensive statistics from your database tables

      // Fetch blog posts count
      try {
        const { count: blogCount } = await supabase
          .from('blog_posts')
          .select('*', { count: 'exact', head: true });
        statsData.blogPosts = blogCount || 0;
      } catch (error) {
        console.error('Error fetching blog posts:', error);
      }

      // Fetch FAQs count
      try {
        const { count: faqCount } = await supabase
          .from('faqs')
          .select('*', { count: 'exact', head: true });
        statsData.faqs = faqCount || 0;
      } catch (error) {
        console.error('Error fetching FAQs:', error);
      }

      // Fetch help articles count
      try {
        const { count: helpCount } = await supabase
          .from('help_articles')
          .select('*', { count: 'exact', head: true });
        statsData.helpArticles = helpCount || 0;
      } catch (error) {
        console.error('Error fetching help articles:', error);
      }

      // Fetch community discussions count
      try {
        const { count: discussionCount } = await supabase
          .from('community_discussions')
          .select('*', { count: 'exact', head: true });
        statsData.communityDiscussions = discussionCount || 0;
      } catch (error) {
        console.error('Error fetching community discussions:', error);
      }

      // Fetch GRA credentials count (users with GRA setup)
      try {
        const { count: graCount } = await supabase
          .from('gra_credentials')
          .select('*', { count: 'exact', head: true });
        statsData.graCredentials = graCount || 0;
      } catch (error) {
        console.error('Error fetching GRA credentials:', error);
      }

      // Fetch Paystack integrations count
      try {
        const { count: paystackCount } = await supabase
          .from('paystack_integrations')
          .select('*', { count: 'exact', head: true });
        statsData.paystackIntegrations = paystackCount || 0;
      } catch (error) {
        console.error('Error fetching Paystack integrations:', error);
      }

      // Fetch email notifications count (recent activity)
      try {
        const { count: emailCount } = await supabase
          .from('email_notifications')
          .select('*', { count: 'exact', head: true });
        statsData.emailNotifications = emailCount || 0;
      } catch (error) {
        console.error('Error fetching email notifications:', error);
      }

      // Fetch recent activity with comprehensive data
      try {
        const { data: recentInvoices, error: activityError } = await supabase
          .from('invoices')
          .select(`
            id,
            invoice_number,
            status,
            total_amount,
            created_at,
            user_id,
            client_id
          `)
          .order('created_at', { ascending: false })
          .limit(10);

        if (!activityError && recentInvoices) {
          // Get user details from auth.users and profiles
          const userIds = [...new Set(recentInvoices.map(inv => inv.user_id).filter(Boolean))];
          const clientIds = [...new Set(recentInvoices.map(inv => inv.client_id).filter(Boolean))];

          const { data: users } = await supabase
            .from('users')
            .select('id, email, raw_user_meta_data')
            .in('id', userIds);

          const { data: clients } = await supabase
            .from('clients')
            .select('id, name')
            .in('id', clientIds);

          const userMap = new Map(users?.map(user => [
            user.id,
            user.raw_user_meta_data?.full_name || user.email || 'Unknown User'
          ]) || []);

          const clientMap = new Map(clients?.map(client => [client.id, client.name]) || []);

          statsData.recentActivity = recentInvoices.map(invoice => ({
            id: invoice.id,
            type: 'invoice_created',
            description: `Invoice ${invoice.invoice_number} created`,
            user: userMap.get(invoice.user_id) || 'Unknown User',
            client: clientMap.get(invoice.client_id) || 'No Client',
            amount: invoice.total_amount,
            status: invoice.status,
            timestamp: invoice.created_at
          }));
        }
      } catch (error) {
        console.error('Error fetching recent activity:', error);
      }

      // Update stats state
      setStats(statsData);
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      setError('An error occurred while fetching dashboard statistics.');
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <div className="text-red-500 mb-4">{error}</div>
          <Button onClick={() => window.history.back()}>Go Back</Button>
        </div>
      </AdminLayout>
    );
  }

  if (!isAdmin) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <div className="text-red-500 mb-4">You do not have permission to access this page.</div>
          <Button onClick={() => window.history.back()}>Go Back</Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex flex-col gap-5">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
          <Button
            onClick={fetchDashboardStats}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh Data
          </Button>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Users"
            value={stats.totalUsers.toString()}
            description="Total registered users"
            icon={<Users className="h-4 w-4" />}
            trend="up"
            trendValue={`+${stats.newUsers} this month`}
          />
          <StatCard
            title="Active Users"
            value={stats.activeUsers.toString()}
            description="Users with at least one invoice"
            icon={<UserPlus className="h-4 w-4" />}
            trend="up"
            trendValue={`${Math.round((stats.activeUsers / stats.totalUsers) * 100) || 0}% of total`}
          />
          <StatCard
            title="Total Revenue"
            value={`GHS ${stats.totalRevenue.toFixed(2)}`}
            description="From all paid invoices"
            icon={<DollarSign className="h-4 w-4" />}
            trend="up"
            trendValue={`GHS ${stats.monthlyRevenue.toFixed(2)} this month`}
          />
          <StatCard
            title="Active Subscriptions"
            value={stats.activeSubscriptions.toString()}
            description="Current paying subscribers"
            icon={<CreditCard className="h-4 w-4" />}
            trend="up"
            trendValue={`${Math.round((stats.activeSubscriptions / stats.totalUsers) * 100) || 0}% conversion`}
          />
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Invoices"
            value={stats.totalInvoices.toString()}
            description="All invoices created"
            icon={<FileText className="h-4 w-4" />}
            trend="up"
            trendValue={`${stats.paidInvoices} paid`}
          />
          <StatCard
            title="Total Clients"
            value={stats.totalClients.toString()}
            description="Registered clients"
            icon={<Users className="h-4 w-4" />}
            trend="up"
            trendValue={`${Math.round(stats.totalClients / stats.activeUsers) || 0} avg per user`}
          />
          <StatCard
            title="Average Invoice"
            value={`GHS ${stats.averageInvoiceValue.toFixed(2)}`}
            description="Average invoice value"
            icon={<TrendingUp className="h-4 w-4" />}
            trend="up"
            trendValue={`${stats.overdueInvoices} overdue`}
          />
          <StatCard
            title="GRA Integrations"
            value={stats.graCredentials.toString()}
            description="Users with GRA setup"
            icon={<CreditCard className="h-4 w-4" />}
            trend="up"
            trendValue={`${stats.paystackIntegrations} Paystack setups`}
          />
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Blog Posts"
            value={stats.blogPosts.toString()}
            description="Published blog articles"
            icon={<Newspaper className="h-4 w-4" />}
            trend="up"
            trendValue={`${stats.helpArticles} help articles`}
          />
          <StatCard
            title="Community"
            value={stats.communityDiscussions.toString()}
            description="Active discussions"
            icon={<Users className="h-4 w-4" />}
            trend="up"
            trendValue={`${stats.faqs} FAQs available`}
          />
          <StatCard
            title="Email Notifications"
            value={stats.emailNotifications.toString()}
            description="Total notifications sent"
            icon={<FileUp className="h-4 w-4" />}
            trend="up"
            trendValue={`${stats.sentInvoices} sent invoices`}
          />
          <StatCard
            title="System Health"
            value={`${Math.round((stats.activeUsers / stats.totalUsers) * 100) || 0}%`}
            description="User engagement rate"
            icon={<TrendingUp className="h-4 w-4" />}
            trend="up"
            trendValue={`${stats.paymentIntegrations} integrations`}
          />
        </div>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
              <Card className="col-span-4">
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-8">
                    {stats.recentActivity.length > 0 ? (
                      stats.recentActivity.slice(0, 5).map((activity, index) => (
                        <div key={activity.id || index} className="flex items-center">
                          <div className="space-y-1">
                            <p className="text-sm font-medium leading-none">{activity.description}</p>
                            <p className="text-sm text-muted-foreground">
                              {activity.user} • {activity.client} • {new Date(activity.timestamp).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="ml-auto font-medium">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              activity.status === 'paid' ? 'bg-green-100 text-green-800' :
                              activity.status === 'sent' ? 'bg-blue-100 text-blue-800' :
                              activity.status === 'overdue' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {activity.status}
                            </span>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center text-muted-foreground">
                        <p>No recent activity</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
              <Card className="col-span-3">
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>Common administrative tasks</CardDescription>
                </CardHeader>
                <CardContent className="grid gap-2">
                  <Button className="w-full justify-start">
                    <UserPlus className="mr-2 h-4 w-4" />
                    Add New User
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <FileUp className="mr-2 h-4 w-4" />
                    Generate Report
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <Newspaper className="mr-2 h-4 w-4" />
                    Create Announcement
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <GanttChart className="mr-2 h-4 w-4" />
                    View System Status
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="analytics" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>User Analytics</CardTitle>
                  <CardDescription>User registration and activity metrics</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Users</span>
                    <span className="text-2xl font-bold">{stats.totalUsers}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Active Users</span>
                    <span className="text-2xl font-bold text-green-600">{stats.activeUsers}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">New Users (30 days)</span>
                    <span className="text-2xl font-bold text-blue-600">{stats.newUsers}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Engagement Rate</span>
                    <span className="text-2xl font-bold text-purple-600">
                      {Math.round((stats.activeUsers / stats.totalUsers) * 100) || 0}%
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Business Analytics</CardTitle>
                  <CardDescription>Revenue and invoice metrics</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Revenue</span>
                    <span className="text-2xl font-bold text-green-600">GHS {stats.totalRevenue.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Monthly Revenue</span>
                    <span className="text-2xl font-bold text-blue-600">GHS {stats.monthlyRevenue.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Average Invoice</span>
                    <span className="text-2xl font-bold text-purple-600">GHS {stats.averageInvoiceValue.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Payment Rate</span>
                    <span className="text-2xl font-bold text-orange-600">
                      {Math.round((stats.paidInvoices / stats.totalInvoices) * 100) || 0}%
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Content Analytics</CardTitle>
                  <CardDescription>Blog, help, and community content</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Blog Posts</span>
                    <span className="text-2xl font-bold">{stats.blogPosts}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Help Articles</span>
                    <span className="text-2xl font-bold text-blue-600">{stats.helpArticles}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">FAQs</span>
                    <span className="text-2xl font-bold text-green-600">{stats.faqs}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Community Discussions</span>
                    <span className="text-2xl font-bold text-purple-600">{stats.communityDiscussions}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Integration Analytics</CardTitle>
                  <CardDescription>Payment and GRA integration usage</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">GRA Credentials</span>
                    <span className="text-2xl font-bold text-green-600">{stats.graCredentials}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Paystack Integrations</span>
                    <span className="text-2xl font-bold text-blue-600">{stats.paystackIntegrations}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Active Subscriptions</span>
                    <span className="text-2xl font-bold text-purple-600">{stats.activeSubscriptions}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Email Notifications</span>
                    <span className="text-2xl font-bold text-orange-600">{stats.emailNotifications}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="reports" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Reports</CardTitle>
                <CardDescription>Generate and view reports</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="report-type">Report Type</Label>
                    <Select defaultValue="users">
                      <SelectTrigger id="report-type">
                        <SelectValue placeholder="Select report type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="users">Users Report</SelectItem>
                        <SelectItem value="invoices">Invoices Report</SelectItem>
                        <SelectItem value="revenue">Revenue Report</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="date-range">Date Range</Label>
                    <Select defaultValue="30days">
                      <SelectTrigger id="date-range">
                        <SelectValue placeholder="Select date range" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="7days">Last 7 Days</SelectItem>
                        <SelectItem value="30days">Last 30 Days</SelectItem>
                        <SelectItem value="90days">Last 90 Days</SelectItem>
                        <SelectItem value="year">This Year</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <Button className="w-full mt-4">
                  <ScrollText className="mr-2 h-4 w-4" />
                  Generate Report
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="notifications" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Notifications</CardTitle>
                <CardDescription>Manage system notifications</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">New User Registration</p>
                      <p className="text-sm text-muted-foreground">Notify when a new user registers</p>
                    </div>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Payment Received</p>
                      <p className="text-sm text-muted-foreground">Notify when a payment is received</p>
                    </div>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">System Alerts</p>
                      <p className="text-sm text-muted-foreground">Notify about system issues</p>
                    </div>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full">Save Notification Settings</Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
