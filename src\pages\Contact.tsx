import React, { useState, useEffect } from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { toast } from '@/hooks/use-toast';
import {
  Mail, Phone, MapPin, Loader2, Clock, MessageSquare,
  Send, CheckCircle, HelpCircle, ArrowRight, Calendar, Globe
} from 'lucide-react';

const Contact: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Trigger animations after component mounts
    setIsVisible(true);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name || !email || !message) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      toast({
        title: 'Success',
        description: 'Your message has been sent. We\'ll get back to you soon!'
      });

      // Reset form
      setName('');
      setEmail('');
      setSubject('');
      setMessage('');
      setIsSubmitting(false);
    }, 1500);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow pt-16">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-gradient-to-b from-white via-gray-50 to-white dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 py-24 md:py-32">
          {/* Animated background elements */}
          <div className="absolute inset-0 ghana-pattern opacity-10 z-0"></div>
          <div className="absolute top-20 left-10 w-64 h-64 bg-ghana-gold/20 dark:bg-ghana-gold/30 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-72 h-72 bg-ghana-green/20 dark:bg-ghana-green/30 rounded-full blur-3xl animate-pulse"></div>

          {/* Small decorative elements */}
          <div className="absolute top-40 left-[15%] w-6 h-6 bg-ghana-gold/40 dark:bg-ghana-gold/50 rounded-full animate-float"></div>
          <div className="absolute top-60 right-[20%] w-4 h-4 bg-ghana-green/40 dark:bg-ghana-green/50 rounded-full animate-bounce"></div>
          <div className="absolute bottom-40 left-[30%] w-5 h-5 bg-ghana-red/30 dark:bg-ghana-red/40 rounded-full animate-bounce"></div>

          <div className="container relative z-10 px-4">
            <div className={`text-center max-w-4xl mx-auto transition-all duration-1000 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
              <div className="inline-block bg-ghana-gold/20 dark:bg-ghana-gold/30 px-4 py-1.5 rounded-full mb-6 animate-pulse">
                <span className="text-sm font-semibold text-ghana-black dark:text-ghana-gold flex items-center">
                  <MessageSquare className="h-4 w-4 mr-1.5 text-ghana-gold" />
                  Get in Touch
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-display mb-6 leading-tight dark:text-foreground">
                Contact <span className="text-ghana-green dark:text-ghana-gold relative">
                  Us
                  <span className="absolute -bottom-2 left-0 w-full h-1 bg-ghana-gold/60 rounded-full"></span>
                </span>
              </h1>

              <p className="text-xl text-gray-600 dark:text-gray-300 mb-10 max-w-3xl mx-auto leading-relaxed">
                Have questions or need help? We're here for you. Reach out to our team and we'll get back to you as soon as possible.
              </p>

              <div className="flex flex-wrap justify-center gap-6 mt-8">
                <div className="flex items-center bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm py-3 px-5 rounded-full shadow-sm">
                  <Mail className="h-5 w-5 text-ghana-green  dark:text-ghana-gold mr-2" />
                  <span className="text-sm font-medium dark:text-white"><EMAIL></span>
                </div>
                <div className="flex items-center bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm py-3 px-5 rounded-full shadow-sm">
                  <Phone className="h-5 w-5 text-ghana-green dark: dark:text-ghana-gold mr-2" />
                  <span className="text-sm font-medium dark:text-white">+233 30 123 4567</span>
                </div>
                <div className="flex items-center bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm py-3 px-5 rounded-full shadow-sm">
                  <Clock className="h-5 w-5 text-ghana-green  dark:text-ghana-gold mr-2" />
                  <span className="text-sm font-medium dark:text-white">Mon-Fri: 9AM-5PM</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Form and Info */}
        <section className="py-20 relative overflow-hidden">
          {/* Background decorative elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-ghana-green/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-ghana-gold/5 rounded-full blur-3xl"></div>

          <div className="container mx-auto px-4 relative z-10">
            <div className={`grid grid-cols-1 lg:grid-cols-2 gap-16 max-w-6xl mx-auto transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
              {/* Contact Form */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 border border-gray-100 dark:border-gray-700 transition-all duration-500 hover:shadow-2xl relative overflow-hidden">
                {/* Decorative corner accent */}
                <div className="absolute -top-10 -left-10 w-40 h-40 bg-ghana-green/10 dark:bg-ghana-green/20 rounded-full blur-xl"></div>
                <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-ghana-gold/10 dark:bg-ghana-gold/20 rounded-full blur-xl"></div>

                <div className="relative">
                  <div className="inline-block bg-ghana-green/10 dark:bg-ghana-green/20 px-4 py-1.5 rounded-full mb-4">
                    <span className="text-sm font-semibold text-ghana-green dark:text-ghana-gold flex items-center">
                      <Send className="h-4 w-4 mr-1.5" />
                      Send a Message
                    </span>
                  </div>

                  <h2 className="text-2xl font-bold mb-6 dark:text-foreground">Get in Touch</h2>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-gray-700 dark:text-gray-200 font-medium">Your Name *</Label>
                        <Input
                          id="name"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          required
                          className="border-gray-300 dark:border-gray-600 focus:border-ghana-green dark:focus:border-ghana-gold focus:ring-ghana-green/20 dark:focus:ring-ghana-gold/20 transition-all duration-300 dark:bg-gray-700 dark:text-gray-200"
                          placeholder="John Doe"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-gray-700 dark:text-gray-200 font-medium">Your Email *</Label>
                        <Input
                          id="email"
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                          className="border-gray-300 dark:border-gray-600 focus:border-ghana-green dark:focus:border-ghana-gold focus:ring-ghana-green/20 dark:focus:ring-ghana-gold/20 transition-all duration-300 dark:bg-gray-700 dark:text-gray-200"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="subject" className="text-gray-700 dark:text-gray-200 font-medium">Subject</Label>
                      <Input
                        id="subject"
                        value={subject}
                        onChange={(e) => setSubject(e.target.value)}
                        className="border-gray-300 dark:border-gray-600 focus:border-ghana-green dark:focus:border-ghana-gold focus:ring-ghana-green/20 dark:focus:ring-ghana-gold/20 transition-all duration-300 dark:bg-gray-700 dark:text-gray-200"
                        placeholder="How can we help you?"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message" className="text-gray-700 dark:text-gray-200 font-medium">Your Message *</Label>
                      <Textarea
                        id="message"
                        rows={6}
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        required
                        className="border-gray-300 dark:border-gray-600 focus:border-ghana-green dark:focus:border-ghana-gold focus:ring-ghana-green/20 dark:focus:ring-ghana-gold/20 transition-all duration-300 dark:bg-gray-700 dark:text-gray-200"
                        placeholder="Please provide details about your inquiry..."
                      />
                    </div>

                    <div className="pt-2">
                      <Button
                        type="submit"
                        className="w-full bg-ghana-green hover:bg-ghana-green/90 dark:bg-ghana-green/90 dark:hover:bg-ghana-green py-6 transition-all duration-300 hover:shadow-lg group"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          <>
                            Send Message
                            <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                          </>
                        )}
                      </Button>
                    </div>

                    <div className="text-center text-sm text-gray-500 dark:text-gray-400 mt-4">
                      <p>We respect your privacy and will never share your information.</p>
                    </div>
                  </form>
                </div>
              </div>

              {/* Contact Information */}
              <div className="flex flex-col justify-between">
                <div>
                  <div className="inline-block bg-ghana-gold/20 dark:bg-ghana-gold/30 px-4 py-1.5 rounded-full mb-4">
                    <span className="text-sm font-semibold text-ghana-black dark:text-ghana-gold flex items-center">
                      <Phone className="h-4 w-4 mr-1.5 text-ghana-gold" />
                      Contact Details
                    </span>
                  </div>

                  <h2 className="text-2xl font-bold mb-8 dark:text-foreground">Contact Information</h2>

                  <div className="space-y-8">
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md border border-gray-100 dark:border-gray-700 transition-all duration-300 hover:shadow-lg hover:border-ghana-green/20 dark:hover:border-ghana-gold/30 flex items-start">
                      <div className="mr-5 mt-1">
                        <div className="w-12 h-12 bg-ghana-green/10 dark:bg-ghana-green/20 rounded-full flex items-center justify-center">
                          <Mail className="h-6 w-6 text-ghana-green dark:text-ghana-gold" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold mb-3 dark:text-gray-200">Email Us</h3>
                        <div className="space-y-3">
                          <div>
                            <p className="text-gray-600 dark:text-gray-400 text-sm mb-1">General Inquiries:</p>
                            <a href="mailto:<EMAIL>" className="text-gray-800 dark:text-gray-300 font-medium hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                              <EMAIL>
                            </a>
                          </div>
                          <div>
                            <p className="text-gray-600 dark:text-gray-400 text-sm mb-1">Support:</p>
                            <a href="mailto:<EMAIL>" className="text-gray-800 dark:text-gray-300 font-medium hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                              <EMAIL>
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md border border-gray-100 dark:border-gray-700 transition-all duration-300 hover:shadow-lg hover:border-ghana-green/20 dark:hover:border-ghana-gold/30 flex items-start">
                      <div className="mr-5 mt-1">
                        <div className="w-12 h-12 bg-ghana-green/10 dark:bg-ghana-green/20 rounded-full flex items-center justify-center">
                          <Phone className="h-6 w-6 text-ghana-green dark:text-ghana-gold" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold mb-3 dark:text-gray-200">Call Us</h3>
                        <div className="space-y-3">
                          <div>
                            <p className="text-gray-600 dark:text-gray-400 text-sm mb-1">Customer Support:</p>
                            <a href="tel:+233301234567" className="text-gray-800 dark:text-gray-300 font-medium hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                              +233 30 123 4567
                            </a>
                          </div>
                          <div>
                            <p className="text-gray-600 dark:text-gray-400 text-sm mb-1">Sales:</p>
                            <a href="tel:+233307654321" className="text-gray-800 dark:text-gray-300 font-medium hover:text-ghana-green dark:hover:text-ghana-gold transition-colors">
                              +233 30 765 4321
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md border border-gray-100 dark:border-gray-700 transition-all duration-300 hover:shadow-lg hover:border-ghana-green/20 dark:hover:border-ghana-gold/30 flex items-start">
                      <div className="mr-5 mt-1">
                        <div className="w-12 h-12 bg-ghana-green/10 dark:bg-ghana-green/20 rounded-full flex items-center justify-center">
                          <MapPin className="h-6 w-6 text-ghana-green dark:text-ghana-gold" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold mb-3 dark:text-gray-200">Visit Us</h3>
                        <p className="text-gray-600 dark:text-gray-400 text-sm mb-1">Office Address:</p>
                        <p className="text-gray-800 dark:text-gray-300">
                          14 Independence Avenue<br />
                          Accra Business District<br />
                          Accra, Ghana
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-10 bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center mb-4">
                    <Calendar className="h-5 w-5 text-ghana-green dark:text-ghana-gold mr-3" />
                    <h3 className="text-lg font-semibold dark:text-gray-200">Business Hours</h3>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-gray-600 dark:text-gray-400 flex items-center">
                        <span className="w-3 h-3 bg-green-500 dark:bg-green-600 rounded-full mr-2"></span>
                        Monday - Friday:
                      </span>
                      <span className="text-gray-800 dark:text-gray-300 font-medium">9:00 AM - 5:00 PM</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-gray-600 dark:text-gray-400 flex items-center">
                        <span className="w-3 h-3 bg-yellow-500 dark:bg-yellow-600 rounded-full mr-2"></span>
                        Saturday:
                      </span>
                      <span className="text-gray-800 dark:text-gray-300 font-medium">10:00 AM - 2:00 PM</span>
                    </div>
                    <div className="flex justify-between items-center py-2">
                      <span className="text-gray-600 dark:text-gray-400 flex items-center">
                        <span className="w-3 h-3 bg-red-500 dark:bg-red-600 rounded-full mr-2"></span>
                        Sunday:
                      </span>
                      <span className="text-gray-800 dark:text-gray-300 font-medium">Closed</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Map Section */}
        <section className="py-16 relative overflow-hidden">
          {/* Background decorative elements */}
          <div className="absolute top-0 left-0 w-64 h-64 bg-ghana-gold/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-ghana-green/5 rounded-full blur-3xl"></div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-10">
                <div className="inline-block bg-ghana-green/10 dark:bg-ghana-green/20 px-4 py-1.5 rounded-full mb-4">
                  <span className="text-sm font-semibold text-ghana-green dark:text-ghana-gold flex items-center">
                    <Globe className="h-4 w-4 mr-1.5" />
                    Our Location
                  </span>
                </div>

                <h2 className="text-3xl font-bold mb-4 dark:text-foreground">Find Us on the Map</h2>
                <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
                  We're conveniently located in the heart of Accra's Business District, easily accessible by public transportation and with ample parking nearby.
                </p>
              </div>

              <div className="relative">
                <div className="absolute -top-6 -left-6 w-24 h-24 bg-ghana-gold/20 rounded-full blur-xl"></div>
                <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-ghana-green/20 rounded-full blur-xl"></div>

                <div className="relative rounded-xl overflow-hidden shadow-xl border-4 border-white transition-all duration-500 hover:shadow-2xl">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3970.9285254127064!2d-0.19984492414567!3d5.5600380344844!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xfdf9a53a68a6d49%3A0x4e3562ae10c0ed73!2sAccra%20Business%20District%2C%20Accra%2C%20Ghana!5e0!3m2!1sen!2sus!4v1659123456789!5m2!1sen!2sus"
                    width="100%"
                    height="500"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title="Payvoicer Office Location"
                  ></iframe>
                </div>
              </div>

              <div className="flex flex-wrap justify-center gap-6 mt-8">
                <div className="flex items-center bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm py-3 px-5 rounded-full shadow-sm">
                  <MapPin className="h-5 w-5 text-ghana-green  dark:text-ghana-gold mr-2" />
                  <span className="text-sm font-medium dark:text-white">14 Independence Avenue, Accra</span>
                </div>
                <div className="flex items-center bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm py-3 px-5 rounded-full shadow-sm">
                  <Clock className="h-5 w-5 text-ghana-green  dark:text-ghana-gold mr-2" />
                  <span className="text-sm font-medium dark:text-white">Open Monday-Friday: 9AM-5PM</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-950 relative overflow-hidden">
          {/* Background decorative elements */}
          <div className="absolute top-0 left-0 w-64 h-64 bg-ghana-gold/5 dark:bg-ghana-gold/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-ghana-green/5 dark:bg-ghana-green/10 rounded-full blur-3xl"></div>

          {/* Small decorative elements */}
          <div className="absolute top-40 right-[15%] w-6 h-6 bg-ghana-gold/20 dark:bg-ghana-gold/30 rounded-full animate-float"></div>
          <div className="absolute bottom-60 left-[20%] w-4 h-4 bg-ghana-green/20 dark:bg-ghana-green/30 rounded-full animate-bounce"></div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-16">
                <div className="inline-block bg-ghana-gold/20 dark:bg-ghana-gold/30 px-4 py-1.5 rounded-full mb-4">
                  <span className="text-sm font-semibold text-ghana-black dark:text-ghana-gold flex items-center">
                    <HelpCircle className="h-4 w-4 mr-1.5 text-ghana-gold" />
                    Questions & Answers
                  </span>
                </div>

                <h2 className="text-3xl md:text-4xl font-bold font-display mb-4 dark:text-foreground">
                  Frequently Asked <span className="text-ghana-green dark:text-ghana-gold">Questions</span>
                </h2>
                <p className="text-gray-600 dark:text-gray-300 text-lg">
                  Find answers to common questions about our support and services
                </p>
              </div>

              <div className="space-y-6">
                {[
                  {
                    question: "How quickly can I expect a response?",
                    answer: "We typically respond to all inquiries within 24 hours during business days. For urgent support issues, we aim to respond within 4 hours."
                  },
                  {
                    question: "Do you offer on-site training?",
                    answer: "Yes, we offer on-site training for business customers in Accra. For locations outside Accra or for our Freelancer plan customers, we provide comprehensive virtual training sessions."
                  },
                  {
                    question: "How can I report a technical issue?",
                    answer: "You can report technical issues through our support portal, <NAME_EMAIL>, or by calling our customer support line. Please include as much detail as possible about the issue you're experiencing."
                  },
                  {
                    question: "What are your support hours?",
                    answer: "Our support team is available Monday through Friday from 9:00 AM to 5:00 PM, and Saturday from 10:00 AM to 2:00 PM (Ghana time). We're closed on Sundays and public holidays."
                  }
                ].map((faq, index) => (
                  <div
                    key={index}
                    className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-8 border border-gray-100 dark:border-gray-700 transition-all duration-300 hover:shadow-lg hover:border-ghana-green/20 dark:hover:border-ghana-gold/30 group"
                  >
                    <h3 className="text-xl font-semibold mb-4 flex items-start group-hover:text-ghana-green dark:text-foreground dark:group-hover:text-ghana-gold transition-colors duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3 text-ghana-green dark:text-ghana-gold flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      {faq.question}
                    </h3>
                    <p className="text-gray-700 dark:text-gray-300 ml-9">
                      {faq.answer}
                    </p>
                  </div>
                ))}
              </div>

              <div className="mt-12 text-center">
                <p className="text-gray-600 dark:text-gray-300 mb-6">Still have questions? We're here to help.</p>
                <Button variant="outline" className="border-2 border-ghana-green text-ghana-green hover:bg-ghana-green/10 transition-all duration-300 group dark:border-ghana-gold dark:text-ghana-gold dark:hover:bg-ghana-gold/20">
                  View All FAQs
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-br from-ghana-green to-ghana-green-dark dark:from-ghana-green/90 dark:to-ghana-green-dark/90 text-white relative overflow-hidden">
          {/* Background decorative elements */}
          <div className="absolute inset-0 ghana-pattern opacity-10 z-0"></div>
          <div className="absolute top-0 left-0 w-full h-24 bg-gradient-to-b from-black/20 to-transparent"></div>
          <div className="absolute bottom-0 left-0 w-full h-24 bg-gradient-to-t from-black/20 to-transparent"></div>

          {/* Animated dots */}
          <div className="absolute top-20 left-10 w-3 h-3 bg-white/30 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-20 w-2 h-2 bg-white/30 rounded-full animate-float"></div>
          <div className="absolute bottom-20 left-1/4 w-4 h-4 bg-white/20 rounded-full animate-bounce"></div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to get started?</h2>
              <p className="text-xl mb-8 max-w-2xl mx-auto">
                Join thousands of businesses in Ghana who trust Payvoicer for their GRA-compliant invoicing needs.
              </p>

              <div className="flex flex-col sm:flex-row justify-center gap-6">
                <Button className="bg-white text-ghana-green hover:bg-ghana-gold hover:text-white px-8 py-6 text-lg shadow-xl transition-all duration-300 hover:shadow-2xl hover:scale-105 group">
                  Sign Up for Free
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>

                <Button variant="outline" className="border-2 border-ghana-green text-ghana-green hover:bg-ghana-green hover:text-white px-8 py-6 text-lg transition-all duration-300">
                  Schedule a Demo
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Contact;
