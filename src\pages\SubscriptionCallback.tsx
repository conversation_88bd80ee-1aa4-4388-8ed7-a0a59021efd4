import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { processSubscriptionCallback } from '@/services/subscriptionService';
import { processPlanChange } from '@/services/enhancedSubscriptionService';
import { useSession } from '@/contexts/SessionContext';
import { useSubscription } from '@/contexts/SubscriptionContext';

const SubscriptionCallback: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useSession();
  const { refreshSubscription } = useSubscription();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing your subscription payment...');

  useEffect(() => {
    const processPayment = async () => {
      try {
        // Check if the payment was cancelled
        const wasCancelled = localStorage.getItem('paystack_payment_cancelled') === 'true';
        if (wasCancelled) {
          console.log("Payment was cancelled by the user");
          // Clear the cancellation flag
          localStorage.removeItem('paystack_payment_cancelled');
          setStatus('error');
          setMessage('Payment was cancelled. Your subscription has not been changed.');

          // Redirect to settings page after 3 seconds
          setTimeout(() => {
            navigate('/settings?tab=billing');
          }, 3000);
          return;
        }

        // Get reference from URL query parameters
        const params = new URLSearchParams(location.search);
        const reference = params.get('reference');
        const trxref = params.get('trxref');
        const status = params.get('status');

        console.log('Subscription callback received:', { reference, trxref, status });

        // Check if status indicates failure
        if (status && status.toLowerCase() !== 'success') {
          setStatus('error');
          setMessage(`Payment was not successful (Status: ${status}). Your subscription has not been changed.`);
          return;
        }

        // For Paystack inline checkout, sometimes we don't get parameters in the URL
        // In this case, we'll try to use the stored reference from localStorage
        let paymentReference = reference || trxref;

        if (!paymentReference) {
          // Try to get reference from localStorage
          paymentReference = localStorage.getItem('paystack_reference');
          console.log('Using reference from localStorage:', paymentReference);

          if (!paymentReference) {
            // If still no reference, try to create a mock reference
            const selectedPlan = localStorage.getItem('selectedPlan');
            if (selectedPlan) {
              paymentReference = `MOCK-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
              console.log('Created mock reference:', paymentReference);
            } else {
              setStatus('error');
              setMessage('Payment reference not found. Please contact support.');
              return;
            }
          }
        }

        // Determine if this is a new enhanced plan change or legacy subscription
        const isEnhancedPlanChange = paymentReference.startsWith('IMM-') || paymentReference.startsWith('SCH-');

        if (isEnhancedPlanChange) {
          // Process using enhanced subscription service
          const result = await processPlanChange(paymentReference, user.id);

          if (result.success) {
            // Clear the stored reference and other payment data
            localStorage.removeItem('paystack_reference');
            localStorage.removeItem('paystack_payment_status');
            localStorage.removeItem('paystack_payment_cancelled');

            setStatus('success');
            setMessage(result.message);

            // Update the subscription context
            await refreshSubscription();

            toast({
              title: 'Plan Change Successful',
              description: result.message,
            });

            // Redirect to settings page after 3 seconds
            setTimeout(() => {
              navigate('/settings?tab=billing');
            }, 3000);
          } else {
            setStatus('error');
            setMessage(result.message || 'Failed to process your plan change.');
          }
        } else {
          // Process using legacy subscription service
          const subscription = await processSubscriptionCallback(paymentReference);

          if (subscription) {
            // Clear the stored reference and other payment data
            localStorage.removeItem('paystack_reference');
            localStorage.removeItem('paystack_payment_status');
            localStorage.removeItem('paystack_payment_cancelled');

            setStatus('success');
            setMessage(`Your subscription to the ${subscription.plan_type} plan was successful!`);

            // Update the subscription context
            await refreshSubscription();

            toast({
              title: 'Subscription Upgraded',
              description: `You have successfully upgraded to the ${subscription.plan_type} plan.`,
            });

            // Redirect to settings page after 3 seconds
            setTimeout(() => {
              navigate('/settings?tab=billing');
            }, 3000);
          } else {
            setStatus('error');
            setMessage('Failed to process your subscription. The payment may not have been completed successfully.');
          }
        }
      } catch (error) {
        console.error('Error processing subscription callback:', error);
        setStatus('error');
        setMessage('An error occurred while processing your subscription. Please contact support.');
      }
    };

    if (user) {
      processPayment();
    } else {
      // If user is not logged in, show error
      setStatus('error');
      setMessage('You must be logged in to process a subscription. Please log in and try again.');
    }
  }, [location.search, navigate, user, toast]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full p-8 bg-white rounded-lg shadow-md">
        <div className="text-center">
          {status === 'loading' && (
            <div className="bg-ghana-green/10 p-3 rounded-full inline-flex items-center justify-center mb-4">
              <Loader2 className="h-12 w-12 text-ghana-green mx-auto animate-spin" />
            </div>
          )}

          {status === 'success' && (
            <div className="bg-green-100 p-3 rounded-full inline-flex items-center justify-center mb-4">
              <CheckCircle className="h-12 w-12 text-ghana-green mx-auto" />
            </div>
          )}

          {status === 'error' && (
            <div className="bg-red-100 p-3 rounded-full inline-flex items-center justify-center mb-4">
              <XCircle className="h-12 w-12 text-red-500 mx-auto" />
            </div>
          )}

          <h1 className="text-2xl font-bold mb-2">
            {status === 'loading' ? 'Processing Payment' :
             status === 'success' ? 'Payment Successful' : 'Payment Failed'}
          </h1>

          <p className="text-gray-600 mb-6">{message}</p>

          {status === 'success' && (
            <>
              <p className="text-sm text-gray-500 mb-4">
                Redirecting you to your account settings...
              </p>
              <div className="flex flex-col gap-4 mt-4">
                <Button
                  className="bg-ghana-green hover:bg-ghana-green/90"
                  onClick={() => navigate('/dashboard')}
                >
                  Go to Dashboard
                </Button>
              </div>
            </>
          )}

          {status === 'error' && (
            <div className="flex flex-col gap-4">
              <Button
                className="bg-ghana-green hover:bg-ghana-green/90"
                onClick={() => navigate('/settings?tab=billing')}
              >
                Try Again
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/dashboard')}
              >
                Return to Dashboard
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubscriptionCallback;
