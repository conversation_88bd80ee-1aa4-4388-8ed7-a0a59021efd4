import { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  UserPlus,
  Filter,
  MoreHorizontal,
  Edit,
  Trash,
  Shield,
  ShieldOff,
  CheckCircle,
  XCircle,
  Download,
  RefreshCw,
  Eye,
  Mail,
  Phone,
  Building,
  Calendar,
  Activity,
  TrendingUp,
  Users as UsersIcon,
  Crown,
  AlertTriangle,
  ChevronDown,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { useSession } from '@/contexts/SessionContext';
import { supabase } from '@/integrations/supabase/client';
import { isAdmin, isAdminEmail } from '@/utils/adminUtils';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';

interface User {
  id: string;
  email: string;
  full_name: string;
  created_at: string;
  updated_at: string;
  last_sign_in_at: string | null;
  is_admin: boolean;
  is_verified: boolean;
  subscription_tier: string | null;
  subscription_status: string | null;
  phone_number?: string;
  company_name?: string;
  address?: string;
  avatar_url?: string;
  billing_address?: string;
  country?: string;
  role?: string;
  total_invoices?: number;
  total_revenue?: number;
  last_activity?: string;
  status: 'active' | 'inactive' | 'suspended';
}

interface UserStats {
  totalUsers: number;
  activeUsers: number;
  verifiedUsers: number;
  adminUsers: number;
  subscribedUsers: number;
  newUsersThisMonth: number;
}

const UsersPage = () => {
  const navigate = useNavigate();
  const { session, user } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [userStats, setUserStats] = useState<UserStats>({
    totalUsers: 0,
    activeUsers: 0,
    verifiedUsers: 0,
    adminUsers: 0,
    subscribedUsers: 0,
    newUsersThisMonth: 0
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterRole, setFilterRole] = useState<string>('all');
  const [filterSubscription, setFilterSubscription] = useState<string>('all');
  const [sortField, setSortField] = useState<string>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Helper function to safely generate user initials
  const getUserInitials = (fullName: string | null | undefined, email: string | null | undefined): string => {
    if (fullName && fullName.trim()) {
      const initials = fullName
        .split(' ')
        .map(n => n && n[0] ? n[0] : '')
        .join('')
        .toUpperCase();
      return initials || 'U';
    }

    if (email && email.length > 0) {
      return email[0].toUpperCase();
    }

    return 'U';
  };

  const fetchUsers = useCallback(async () => {
    setIsRefreshing(true);
    try {
      // Sync auth.users to profiles and get accurate user count (same as AdminDashboard)
      const { data: syncResult, error: syncError } = await supabase.rpc('sync_user_data');

      if (!syncError && syncResult) {
        console.log('User sync result:', syncResult);
        if (syncResult.users_synced > 0) {
          toast.info('User data synced', {
            description: `🔄 ${syncResult.users_synced} users synced from auth.users to profiles\n📊 Total users: ${syncResult.profiles_count_after}`,
            duration: 5000
          });
        }
      } else {
        console.log('Sync function not available, using direct profiles count');
        toast.info('Using fallback user count method', {
          description: '⚠️ Auto-sync not available, using profiles table directly\n💡 Click "Refresh" to ensure accurate counts',
          duration: 4000
        });
      }

      // Get total users from profiles table (now synced with auth.users)
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (profilesError) {
        console.error('Error fetching profiles:', profilesError);
        setError('Error fetching user data.');
        toast.error('Failed to fetch users', {
          description: '❌ Could not retrieve user data from database\n🔄 Try refreshing the page',
          duration: 5000
        });
        return;
      }

      // Initialize admin user IDs set
      let adminUserIds = new Set();

      // Check if user_roles table exists and fetch admin roles
      try {
        const { error: tableCheckError } = await supabase
          .from('user_roles')
          .select('count(*)', { count: 'exact', head: true });

        // If the table exists, fetch admin roles
        if (!tableCheckError) {
          const { data: rolesData, error: rolesError } = await supabase
            .from('user_roles')
            .select('*')
            .eq('role', 'admin');

          if (!rolesError && rolesData) {
            // Create a map of admin user IDs for quick lookup
            adminUserIds = new Set(rolesData.map(role => role.user_id));
          }
        } else if (tableCheckError.code === '42P01') {
          console.log('user_roles table does not exist, skipping role check');
        } else {
          console.error('Error checking user_roles table:', tableCheckError);
        }
      } catch (error) {
        console.error('Error fetching admin roles:', error);
      }

      // Initialize subscription tiers map
      const subscriptionTiers = new Map();
      const subscriptionStatuses = new Map();

      // Fetch subscription data
      try {
        const { data: subscriptionsData, error: subscriptionsError } = await supabase
          .from('subscriptions')
          .select('*');

        if (!subscriptionsError && subscriptionsData) {
          // Create maps of subscription data
          subscriptionsData.forEach(sub => {
            subscriptionTiers.set(sub.user_id, sub.tier);
            subscriptionStatuses.set(sub.user_id, sub.status);
          });
        } else if (subscriptionsError && subscriptionsError.code === '42P01') {
          console.log('subscriptions table does not exist, skipping subscription check');
        } else if (subscriptionsError) {
          console.error('Error fetching subscriptions:', subscriptionsError);
        }
      } catch (error) {
        console.error('Error fetching subscription data:', error);
      }

      // Get admin emails from environment variable
      const adminEmails = (import.meta.env.VITE_ADMIN_EMAILS || '').split(',').map(email => email.trim().toLowerCase());

      // Fetch user activity data (invoices, revenue)
      const { data: invoiceData } = await supabase
        .from('invoices')
        .select('user_id, total_amount, status, created_at');

      const userActivityMap = new Map();

      if (invoiceData) {
        invoiceData.forEach(invoice => {
          const userId = invoice.user_id;
          if (!userActivityMap.has(userId)) {
            userActivityMap.set(userId, { count: 0, revenue: 0, lastActivity: null });
          }
          const activity = userActivityMap.get(userId);
          activity.count += 1;
          if (invoice.status === 'paid') {
            activity.revenue += invoice.total_amount || 0;
          }
          if (!activity.lastActivity || new Date(invoice.created_at) > new Date(activity.lastActivity)) {
            activity.lastActivity = invoice.created_at;
          }
          userActivityMap.set(userId, activity);
        });
      }

      // Transform the data with comprehensive information
      const transformedUsers: User[] = profilesData.map(profile => {
        const activity = userActivityMap.get(profile.id) || { count: 0, revenue: 0, lastActivity: null };
        const isAdmin = adminUserIds.has(profile.id) || adminEmails.includes((profile.email || '').toLowerCase());

        return {
          id: profile.id || '',
          email: profile.email || '',
          full_name: profile.full_name || '',
          created_at: profile.created_at || new Date().toISOString(),
          updated_at: profile.updated_at || profile.created_at || new Date().toISOString(),
          last_sign_in_at: profile.last_sign_in_at || null,
          is_admin: isAdmin,
          is_verified: profile.email_verified || false,
          subscription_tier: subscriptionTiers.get(profile.id) || null,
          subscription_status: subscriptionStatuses.get(profile.id) || null,
          phone_number: profile.phone_number || '',
          company_name: profile.company_name || '',
          address: profile.address || '',
          avatar_url: profile.avatar_url || '',
          billing_address: profile.billing_address || '',
          country: profile.country || '',
          role: profile.role || 'freelancer',
          total_invoices: activity.count || 0,
          total_revenue: activity.revenue || 0,
          last_activity: activity.lastActivity || null,
          status: (profile.last_sign_in_at ?
            (new Date(profile.last_sign_in_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) ? 'active' : 'inactive') :
            'inactive') as 'active' | 'inactive' | 'suspended'
        };
      });

      setUsers(transformedUsers);

      // Calculate user statistics
      const stats: UserStats = {
        totalUsers: transformedUsers.length,
        activeUsers: transformedUsers.filter(u => u.status === 'active').length,
        verifiedUsers: transformedUsers.filter(u => u.is_verified).length,
        adminUsers: transformedUsers.filter(u => u.is_admin).length,
        subscribedUsers: transformedUsers.filter(u => u.subscription_tier && u.subscription_tier !== 'free').length,
        newUsersThisMonth: transformedUsers.filter(u => {
          const createdDate = new Date(u.created_at);
          const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          return createdDate > thirtyDaysAgo;
        }).length
      };

      setUserStats(stats);
    } catch (error) {
      console.error('Error fetching users:', error);
      setError('An error occurred while fetching user data.');
      toast.error('Failed to fetch users', {
        description: 'An error occurred while loading user data',
        duration: 5000
      });
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  useEffect(() => {
    const checkAdminAndFetchUsers = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (!session || !user) {
          setIsLoading(false);
          setError('You must be logged in to access the admin dashboard.');
          return;
        }

        const userId = session.user.id;
        const userEmail = user.email || '';

        // Check if user is admin
        try {
          const adminStatus = await isAdmin(userId, userEmail);

          if (adminStatus) {
            setIsAuthorized(true);
            fetchUsers();
          } else {
            setIsAuthorized(false);
            setError('You do not have permission to access this page.');
            // Use a timeout to avoid immediate navigation which can cause issues
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 100);
          }
        } catch (adminError) {
          console.error('Error checking admin status:', adminError);

          // Fallback to just checking email if there's an error with the full check
          if (isAdminEmail(userEmail)) {
            setIsAuthorized(true);
            fetchUsers();
          } else {
            setIsAuthorized(false);
            setError('You do not have permission to access this page.');
            // Use a timeout to avoid immediate navigation which can cause issues
            setTimeout(() => {
              navigate('/dashboard', { replace: true });
            }, 100);
          }
        }
      } catch (error) {
        console.error('Error in admin verification:', error);
        setIsAuthorized(false);
        setError('An error occurred while verifying admin permissions.');
      } finally {
        setIsLoading(false);
      }
    };

    checkAdminAndFetchUsers();
  }, [session, user, navigate, fetchUsers]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const filteredUsers = users.filter(user => {
    const query = searchQuery.toLowerCase();

    // Search filter
    const matchesSearch = !query || (
      user.email.toLowerCase().includes(query) ||
      user.full_name.toLowerCase().includes(query) ||
      (user.subscription_tier && user.subscription_tier.toLowerCase().includes(query)) ||
      (user.company_name && user.company_name.toLowerCase().includes(query))
    );

    // Status filter
    const matchesStatus = filterStatus === 'all' || (
      (filterStatus === 'active' && user.status === 'active') ||
      (filterStatus === 'inactive' && user.status === 'inactive') ||
      (filterStatus === 'verified' && user.is_verified) ||
      (filterStatus === 'unverified' && !user.is_verified)
    );

    // Role filter
    const matchesRole = filterRole === 'all' || (
      (filterRole === 'admin' && user.is_admin) ||
      (filterRole === 'user' && !user.is_admin)
    );

    // Subscription filter
    const matchesSubscription = filterSubscription === 'all' || (
      (filterSubscription === 'free' && (!user.subscription_tier || user.subscription_tier === 'free')) ||
      (filterSubscription !== 'free' && user.subscription_tier === filterSubscription)
    );

    return matchesSearch && matchesStatus && matchesRole && matchesSubscription;
  });

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setIsEditDialogOpen(true);
  };

  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  const handleToggleAdminRole = (user: User) => {
    setSelectedUser(user);
    setIsRoleDialogOpen(true);
  };

  const saveUserChanges = async () => {
    // Implementation would go here
    setIsEditDialogOpen(false);
  };

  const confirmDeleteUser = async () => {
    // Implementation would go here
    setIsDeleteDialogOpen(false);
  };

  const confirmRoleChange = async () => {
    // Implementation would go here
    setIsRoleDialogOpen(false);
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <div className="text-red-500 mb-4">{error}</div>
          <Button onClick={() => window.history.back()}>Go Back</Button>
        </div>
      </AdminLayout>
    );
  }

  if (!isAuthorized) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <div className="text-red-500 mb-4">You do not have permission to access this page.</div>
          <Button onClick={() => navigate('/dashboard')}>Go to Dashboard</Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex flex-col gap-5">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold tracking-tight">Users Management</h1>
          <div className="flex items-center gap-2">
            <Button
              onClick={fetchUsers}
              variant="outline"
              size="sm"
              disabled={isRefreshing}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button>
              <UserPlus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </div>
        </div>

        {/* User Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <UsersIcon className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                  <p className="text-2xl font-bold">{userStats.totalUsers}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Activity className="h-4 w-4 text-green-600" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Active Users</p>
                  <p className="text-2xl font-bold text-green-600">{userStats.activeUsers}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-blue-600" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Verified</p>
                  <p className="text-2xl font-bold text-blue-600">{userStats.verifiedUsers}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Crown className="h-4 w-4 text-purple-600" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Admins</p>
                  <p className="text-2xl font-bold text-purple-600">{userStats.adminUsers}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-4 w-4 text-orange-600" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">Subscribed</p>
                  <p className="text-2xl font-bold text-orange-600">{userStats.subscribedUsers}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 text-indigo-600" />
                <div className="ml-2">
                  <p className="text-sm font-medium text-muted-foreground">New (30d)</p>
                  <p className="text-2xl font-bold text-indigo-600">{userStats.newUsersThisMonth}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Users</CardTitle>
            <CardDescription>Manage your application users and their permissions.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <div className="relative w-full max-w-sm">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search users by name, email, or subscription..."
                  className="pl-8 w-full"
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
              <div className="flex items-center gap-2 ml-2">
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="verified">Verified</SelectItem>
                    <SelectItem value="unverified">Unverified</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filterRole} onValueChange={setFilterRole}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Roles</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="user">User</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filterSubscription} onValueChange={setFilterSubscription}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Plan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Plans</SelectItem>
                    <SelectItem value="free">Free</SelectItem>
                    <SelectItem value="freelancer">Freelancer</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                    <SelectItem value="enterprise">Enterprise</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[250px]">User</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Subscription</TableHead>
                    <TableHead>Activity</TableHead>
                    <TableHead>Revenue</TableHead>
                    <TableHead>Joined</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                        <div className="flex flex-col items-center gap-2">
                          <UsersIcon className="h-8 w-8 text-muted-foreground/50" />
                          <p>No users found</p>
                          <p className="text-sm">Try adjusting your search or filters</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredUsers.map((user) => (
                      <TableRow key={user.id} className="hover:bg-muted/50">
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={user.avatar_url} alt={user.full_name} />
                              <AvatarFallback className="text-xs">
                                {getUserInitials(user.full_name, user.email)}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex flex-col">
                              <span className="font-medium">{user.full_name || 'No Name'}</span>
                              <span className="text-sm text-muted-foreground">{user.email}</span>
                              {user.company_name && (
                                <span className="text-xs text-muted-foreground flex items-center gap-1">
                                  <Building className="h-3 w-3" />
                                  {user.company_name}
                                </span>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col gap-1">
                            <div className="flex items-center gap-2">
                              {user.is_verified ? (
                                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                  <CheckCircle className="mr-1 h-3 w-3" />
                                  Verified
                                </Badge>
                              ) : (
                                <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                                  <XCircle className="mr-1 h-3 w-3" />
                                  Unverified
                                </Badge>
                              )}
                            </div>
                            <Badge
                              variant="outline"
                              className={`text-xs ${
                                user.status === 'active'
                                  ? 'bg-green-50 text-green-600 border-green-200'
                                  : 'bg-gray-50 text-gray-600 border-gray-200'
                              }`}
                            >
                              {user.status === 'active' ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          {user.is_admin ? (
                            <Badge className="bg-purple-100 text-purple-800 border-purple-200">
                              <Crown className="mr-1 h-3 w-3" />
                              Admin
                            </Badge>
                          ) : (
                            <Badge variant="outline">User</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col gap-1">
                            <Badge
                              variant="outline"
                              className={`capitalize ${
                                user.subscription_tier && user.subscription_tier !== 'free'
                                  ? 'bg-blue-50 text-blue-700 border-blue-200'
                                  : 'bg-gray-50 text-gray-500 border-gray-200'
                              }`}
                            >
                              {user.subscription_tier || 'Free'}
                            </Badge>
                            {user.subscription_status && (
                              <span className="text-xs text-muted-foreground capitalize">
                                {user.subscription_status}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col gap-1">
                            <div className="flex items-center gap-1 text-sm">
                              <Activity className="h-3 w-3" />
                              <span>{user.total_invoices || 0} invoices</span>
                            </div>
                            {user.last_activity && (
                              <span className="text-xs text-muted-foreground">
                                Last: {new Date(user.last_activity).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col gap-1">
                            <span className="font-medium text-green-600">
                              GHS {(user.total_revenue || 0).toFixed(2)}
                            </span>
                            {user.total_invoices && user.total_invoices > 0 && (
                              <span className="text-xs text-muted-foreground">
                                Avg: GHS {((user.total_revenue || 0) / user.total_invoices).toFixed(2)}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col gap-1">
                            <span className="text-sm">{new Date(user.created_at).toLocaleDateString()}</span>
                            {user.last_sign_in_at && (
                              <span className="text-xs text-muted-foreground">
                                Last login: {new Date(user.last_sign_in_at).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Open menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => {
                                setSelectedUser(user);
                                setIsViewDialogOpen(true);
                              }}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEditUser(user)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => window.open(`mailto:${user.email}`)}>
                                <Mail className="mr-2 h-4 w-4" />
                                Send Email
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleToggleAdminRole(user)}>
                                {user.is_admin ? (
                                  <>
                                    <ShieldOff className="mr-2 h-4 w-4" />
                                    Remove Admin
                                  </>
                                ) : (
                                  <>
                                    <Shield className="mr-2 h-4 w-4" />
                                    Make Admin
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => handleDeleteUser(user)}
                                className="text-red-600"
                              >
                                <Trash className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-sm text-muted-foreground">
              Showing {filteredUsers.length} of {users.length} users
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="outline" size="sm" disabled>
                Next
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>

      {/* View User Details Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
            <DialogDescription>
              Comprehensive information about this user
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="grid gap-6 py-4">
              {/* User Profile Section */}
              <div className="flex items-start gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={selectedUser.avatar_url} alt={selectedUser.full_name} />
                  <AvatarFallback className="text-lg">
                    {getUserInitials(selectedUser.full_name, selectedUser.email)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold">{selectedUser.full_name || 'No Name'}</h3>
                  <p className="text-muted-foreground">{selectedUser.email}</p>
                  {selectedUser.company_name && (
                    <p className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                      <Building className="h-4 w-4" />
                      {selectedUser.company_name}
                    </p>
                  )}
                  <div className="flex items-center gap-2 mt-2">
                    {selectedUser.is_verified ? (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        Verified
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                        <XCircle className="mr-1 h-3 w-3" />
                        Unverified
                      </Badge>
                    )}
                    {selectedUser.is_admin && (
                      <Badge className="bg-purple-100 text-purple-800 border-purple-200">
                        <Crown className="mr-1 h-3 w-3" />
                        Admin
                      </Badge>
                    )}
                    <Badge
                      variant="outline"
                      className={`${
                        selectedUser.status === 'active'
                          ? 'bg-green-50 text-green-600 border-green-200'
                          : 'bg-gray-50 text-gray-600 border-gray-200'
                      }`}
                    >
                      {selectedUser.status === 'active' ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* User Information Grid */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Contact Information</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{selectedUser.email}</span>
                      </div>
                      {selectedUser.phone_number && (
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{selectedUser.phone_number}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Location</Label>
                    <div className="mt-2 space-y-1">
                      <p className="text-sm">{selectedUser.country || 'Not specified'}</p>
                      {selectedUser.address && (
                        <p className="text-sm text-muted-foreground">{selectedUser.address}</p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Subscription</Label>
                    <div className="mt-2">
                      <Badge
                        variant="outline"
                        className={`capitalize ${
                          selectedUser.subscription_tier && selectedUser.subscription_tier !== 'free'
                            ? 'bg-blue-50 text-blue-700 border-blue-200'
                            : 'bg-gray-50 text-gray-500 border-gray-200'
                        }`}
                      >
                        {selectedUser.subscription_tier || 'Free'}
                      </Badge>
                      {selectedUser.subscription_status && (
                        <p className="text-sm text-muted-foreground mt-1 capitalize">
                          Status: {selectedUser.subscription_status}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Activity</Label>
                    <div className="mt-2 space-y-1">
                      <div className="flex items-center gap-2">
                        <Activity className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{selectedUser.total_invoices || 0} invoices</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">GHS {(selectedUser.total_revenue || 0).toFixed(2)} revenue</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Timestamps */}
              <div className="border-t pt-4">
                <Label className="text-sm font-medium text-muted-foreground">Account Timeline</Label>
                <div className="mt-2 grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Joined:</span>
                    <span className="ml-2">{new Date(selectedUser.created_at).toLocaleDateString()}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Last Updated:</span>
                    <span className="ml-2">{new Date(selectedUser.updated_at).toLocaleDateString()}</span>
                  </div>
                  {selectedUser.last_sign_in_at && (
                    <div>
                      <span className="text-muted-foreground">Last Login:</span>
                      <span className="ml-2">{new Date(selectedUser.last_sign_in_at).toLocaleDateString()}</span>
                    </div>
                  )}
                  {selectedUser.last_activity && (
                    <div>
                      <span className="text-muted-foreground">Last Activity:</span>
                      <span className="ml-2">{new Date(selectedUser.last_activity).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Close
            </Button>
            <Button onClick={() => {
              setIsViewDialogOpen(false);
              handleEditUser(selectedUser!);
            }}>
              Edit User
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Make changes to the user's profile here.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={selectedUser.full_name}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  Email
                </Label>
                <Input
                  id="email"
                  value={selectedUser.email}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="phone" className="text-right">
                  Phone
                </Label>
                <Input
                  id="phone"
                  value={selectedUser.phone_number || ''}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="company" className="text-right">
                  Company
                </Label>
                <Input
                  id="company"
                  value={selectedUser.company_name || ''}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="country" className="text-right">
                  Country
                </Label>
                <Input
                  id="country"
                  value={selectedUser.country || ''}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="subscription" className="text-right">
                  Subscription
                </Label>
                <Select defaultValue={selectedUser.subscription_tier || "free"}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select a subscription" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="free">Free</SelectItem>
                    <SelectItem value="freelancer">Freelancer</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                    <SelectItem value="enterprise">Enterprise</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Status</Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Checkbox id="verified" checked={selectedUser.is_verified} />
                  <label
                    htmlFor="verified"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Email verified
                  </label>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">Admin</Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Checkbox id="admin" checked={selectedUser.is_admin} />
                  <label
                    htmlFor="admin"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Admin privileges
                  </label>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveUserChanges}>Save changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete User</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this user? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="py-4">
              <p><strong>Name:</strong> {selectedUser.full_name || 'N/A'}</p>
              <p><strong>Email:</strong> {selectedUser.email}</p>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteUser}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Change Role Dialog */}
      <Dialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change User Role</DialogTitle>
            <DialogDescription>
              {selectedUser?.is_admin
                ? "Remove admin privileges from this user?"
                : "Grant admin privileges to this user?"}
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="py-4">
              <p><strong>Name:</strong> {selectedUser.full_name || 'N/A'}</p>
              <p><strong>Email:</strong> {selectedUser.email}</p>
              <p><strong>Current Role:</strong> {selectedUser.is_admin ? 'Admin' : 'User'}</p>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRoleDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={confirmRoleChange}>
              {selectedUser?.is_admin ? "Remove Admin Role" : "Grant Admin Role"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default UsersPage;
