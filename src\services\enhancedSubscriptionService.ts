/**
 * Enhanced Subscription Service
 * 
 * Handles subscription logic with scheduled plan changes:
 * - Free to Paid: Immediate activation
 * - Paid to Paid: Scheduled activation at end of current period
 */

import { supabase } from '@/integrations/supabase/client';
import { Tables, TablesInsert } from '@/integrations/supabase/types';
import { initializePaystackTransaction, verifyPaystackTransaction } from './paystackService';
import { PLAN_PRICES } from './secureSubscriptionService';

export interface PlanChangeRequest {
  userId: string;
  currentPlan: string;
  newPlan: string;
  newBillingCycle: 'monthly' | 'yearly';
  userEmail: string;
  callbackUrl: string;
}

export interface PlanChangeResult {
  success: boolean;
  isImmediate: boolean;
  effectiveDate: string;
  paymentUrl?: string;
  scheduledChangeId?: string;
  message: string;
}

/**
 * Check if user is currently on a paid plan
 */
export const isOnPaidPlan = async (userId: string): Promise<boolean> => {
  try {
    const { data: subscription, error } = await supabase
      .from('subscriptions')
      .select('plan_type')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (error || !subscription) {
      return false;
    }

    return subscription.plan_type !== 'free';
  } catch (error) {
    console.error('Error checking if user is on paid plan:', error);
    return false;
  }
};

/**
 * Get the effective date for a plan change
 */
export const getPlanChangeEffectiveDate = async (userId: string): Promise<Date> => {
  try {
    const { data: subscription, error } = await supabase
      .from('subscriptions')
      .select('plan_type, current_period_end')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (error || !subscription) {
      // No subscription or error, return immediate
      return new Date();
    }

    // If user is on free plan, return immediate
    if (subscription.plan_type === 'free') {
      return new Date();
    }

    // If user is on paid plan, return end of current period
    return new Date(subscription.current_period_end || new Date());
  } catch (error) {
    console.error('Error getting plan change effective date:', error);
    return new Date();
  }
};

/**
 * Initialize a plan change (upgrade/downgrade)
 */
export const initializePlanChange = async (request: PlanChangeRequest): Promise<PlanChangeResult> => {
  try {
    const { userId, currentPlan, newPlan, newBillingCycle, userEmail, callbackUrl } = request;

    // Get current subscription
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (subError && subError.code !== 'PGRST116') {
      throw new Error(`Error fetching subscription: ${subError.message}`);
    }

    // Determine if this is immediate or scheduled
    const isCurrentlyOnPaidPlan = await isOnPaidPlan(userId);
    const effectiveDate = await getPlanChangeEffectiveDate(userId);
    const isImmediate = !isCurrentlyOnPaidPlan;

    // Get plan price
    const planPrice = PLAN_PRICES[newPlan as keyof typeof PLAN_PRICES];
    if (!planPrice) {
      throw new Error('Invalid plan type');
    }

    const amount = planPrice[newBillingCycle];
    const amountInKobo = amount * 100;

    // Generate unique reference
    const reference = `${isImmediate ? 'IMM' : 'SCH'}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    // Initialize Paystack transaction
    const transaction = await initializePaystackTransaction({
      email: userEmail,
      amount: amountInKobo,
      currency: 'GHS',
      callback_url: callbackUrl,
      metadata: {
        userId,
        subscriptionId: subscription?.id,
        planType: newPlan,
        period: newBillingCycle,
        currentPlan,
        isImmediate: isImmediate.toString(),
        effectiveDate: effectiveDate.toISOString(),
        transactionType: 'plan_change'
      }
    });

    if (!transaction) {
      throw new Error('Failed to initialize payment');
    }

    return {
      success: true,
      isImmediate,
      effectiveDate: effectiveDate.toISOString(),
      paymentUrl: transaction.authorizationUrl,
      message: isImmediate 
        ? 'Your new plan will be activated immediately after payment'
        : `Your new plan will be activated on ${effectiveDate.toLocaleDateString()}`
    };

  } catch (error) {
    console.error('Error initializing plan change:', error);
    return {
      success: false,
      isImmediate: false,
      effectiveDate: new Date().toISOString(),
      message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

/**
 * Process plan change after payment verification
 */
export const processPlanChange = async (
  reference: string,
  userId: string
): Promise<{ success: boolean; message: string }> => {
  try {
    // Verify payment with Paystack
    const verificationResult = await verifyPaystackTransaction(reference);
    
    if (!verificationResult || verificationResult.status !== 'success') {
      throw new Error('Payment verification failed');
    }

    const metadata = verificationResult.metadata;
    const isImmediate = metadata.isImmediate === 'true';
    const effectiveDate = new Date(metadata.effectiveDate);
    const newPlan = metadata.planType;
    const newBillingCycle = metadata.period;
    const currentPlan = metadata.currentPlan;

    // Get current subscription
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (isImmediate) {
      // Apply plan change immediately
      await applyPlanChangeImmediately(
        userId,
        subscription?.id,
        newPlan,
        newBillingCycle,
        verificationResult.amount / 100,
        reference
      );

      return {
        success: true,
        message: 'Your plan has been upgraded successfully!'
      };
    } else {
      // Schedule plan change for later
      await schedulePlanChange(
        userId,
        subscription.id,
        currentPlan,
        subscription.billing_cycle,
        newPlan,
        newBillingCycle,
        effectiveDate,
        verificationResult.amount / 100,
        reference
      );

      return {
        success: true,
        message: `Your plan change has been scheduled for ${effectiveDate.toLocaleDateString()}. Payment has been processed successfully.`
      };
    }

  } catch (error) {
    console.error('Error processing plan change:', error);
    return {
      success: false,
      message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

/**
 * Apply plan change immediately (for free to paid transitions)
 */
const applyPlanChangeImmediately = async (
  userId: string,
  subscriptionId: string | undefined,
  newPlan: string,
  newBillingCycle: string,
  amount: number,
  reference: string
): Promise<void> => {
  const now = new Date();
  const periodEnd = new Date(
    newBillingCycle === 'yearly' 
      ? now.setFullYear(now.getFullYear() + 1)
      : now.setMonth(now.getMonth() + 1)
  );

  if (subscriptionId) {
    // Update existing subscription
    const { error } = await supabase
      .from('subscriptions')
      .update({
        plan_type: newPlan,
        billing_cycle: newBillingCycle,
        status: 'active',
        current_period_start: new Date().toISOString(),
        current_period_end: periodEnd.toISOString(),
        last_payment_date: new Date().toISOString(),
        next_payment_date: periodEnd.toISOString(),
        paystack_subscription_code: reference,
        updated_at: new Date().toISOString()
      })
      .eq('id', subscriptionId);

    if (error) throw error;
  } else {
    // Create new subscription
    const subscriptionData: TablesInsert<'subscriptions'> = {
      user_id: userId,
      plan_type: newPlan,
      billing_cycle: newBillingCycle,
      status: 'active',
      current_period_start: new Date().toISOString(),
      current_period_end: periodEnd.toISOString(),
      last_payment_date: new Date().toISOString(),
      next_payment_date: periodEnd.toISOString(),
      paystack_subscription_code: reference,
      paystack_customer_code: `CUST_${Math.random().toString(36).substring(2, 10)}`
    };

    const { error } = await supabase
      .from('subscriptions')
      .insert(subscriptionData);

    if (error) throw error;
  }

  // Log transaction
  await supabase
    .from('subscription_transactions')
    .insert({
      user_id: userId,
      subscription_id: subscriptionId,
      transaction_type: 'immediate_upgrade',
      new_plan: newPlan,
      new_billing_cycle: newBillingCycle,
      amount,
      payment_gateway: 'paystack',
      payment_gateway_reference: reference,
      verification_status: 'verified'
    });
};

/**
 * Schedule plan change for later (for paid to paid transitions)
 */
const schedulePlanChange = async (
  userId: string,
  subscriptionId: string,
  currentPlan: string,
  currentBillingCycle: string,
  newPlan: string,
  newBillingCycle: string,
  effectiveDate: Date,
  amount: number,
  reference: string
): Promise<void> => {
  // Create scheduled plan change record
  const { data: scheduledChange, error: scheduleError } = await supabase
    .from('scheduled_plan_changes')
    .insert({
      user_id: userId,
      subscription_id: subscriptionId,
      current_plan_type: currentPlan,
      current_billing_cycle: currentBillingCycle,
      new_plan_type: newPlan,
      new_billing_cycle: newBillingCycle,
      scheduled_date: effectiveDate.toISOString(),
      amount_paid: amount,
      payment_reference: reference,
      status: 'scheduled'
    })
    .select()
    .single();

  if (scheduleError) throw scheduleError;

  // Update subscription to indicate it has a scheduled change
  const { error: updateError } = await supabase
    .from('subscriptions')
    .update({
      has_scheduled_change: true,
      scheduled_plan_type: newPlan,
      scheduled_billing_cycle: newBillingCycle,
      scheduled_change_date: effectiveDate.toISOString(),
      scheduled_change_amount: amount,
      scheduled_change_reference: reference,
      updated_at: new Date().toISOString()
    })
    .eq('id', subscriptionId);

  if (updateError) throw updateError;

  // Log transaction
  await supabase
    .from('subscription_transactions')
    .insert({
      user_id: userId,
      subscription_id: subscriptionId,
      transaction_type: 'scheduled_upgrade',
      previous_plan: currentPlan,
      new_plan: newPlan,
      previous_billing_cycle: currentBillingCycle,
      new_billing_cycle: newBillingCycle,
      amount,
      payment_gateway: 'paystack',
      payment_gateway_reference: reference,
      verification_status: 'verified'
    });
};

/**
 * Get user's scheduled plan changes
 */
export const getScheduledPlanChanges = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('scheduled_plan_changes')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'scheduled')
      .order('scheduled_date', { ascending: true });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching scheduled plan changes:', error);
    return [];
  }
};

/**
 * Cancel a scheduled plan change
 */
export const cancelScheduledPlanChange = async (userId: string, changeId: string) => {
  try {
    const { error } = await supabase
      .from('scheduled_plan_changes')
      .update({ status: 'cancelled' })
      .eq('id', changeId)
      .eq('user_id', userId);

    if (error) throw error;

    // Update subscription to remove scheduled change info
    const { error: updateError } = await supabase
      .from('subscriptions')
      .update({
        has_scheduled_change: false,
        scheduled_plan_type: null,
        scheduled_billing_cycle: null,
        scheduled_change_date: null,
        scheduled_change_amount: null,
        scheduled_change_reference: null
      })
      .eq('user_id', userId);

    if (updateError) throw updateError;

    return { success: true };
  } catch (error) {
    console.error('Error cancelling scheduled plan change:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};
