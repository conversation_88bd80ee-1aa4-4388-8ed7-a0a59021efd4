# Process Scheduled Plans Edge Function

This Supabase Edge Function processes scheduled subscription plan changes that are due to be applied. It replaces the previous Next.js API route for better integration with the Supabase ecosystem.

## Features

- **Automatic Processing**: Processes all scheduled plan changes that are due
- **Cleanup**: Removes old applied changes (older than 30 days)
- **Statistics**: Provides detailed statistics about processed changes
- **Security**: Optional authentication using CRON_SECRET
- **Error Handling**: Comprehensive error handling and logging
- **CORS Support**: Proper CORS headers for web requests

## Environment Variables

The function requires the following environment variables to be set in your Supabase project:

### Required
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key for admin database operations

### Optional
- `CRON_SECRET`: Secret token for authenticating cron job requests

## Deployment

### Using Supabase CLI

1. Make sure you have the Supabase CLI installed and logged in:
```bash
npm install -g supabase
supabase login
```

2. Deploy the function:
```bash
supabase functions deploy process-scheduled-plans
```

3. Set the required environment variables:
```bash
supabase secrets set SUPABASE_URL=your_supabase_url
supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
supabase secrets set CRON_SECRET=your_secure_secret
```

## Usage

### Cron Job Setup

Set up a cron job to call the function periodically:

```bash
# Run every hour
0 * * * * curl -X POST https://your-project.supabase.co/functions/v1/process-scheduled-plans \
  --header 'Authorization: Bearer YOUR_CRON_SECRET' \
  --header 'Content-Type: application/json'
```

### Manual Execution

You can manually trigger the function:

```bash
curl -X POST https://your-project.supabase.co/functions/v1/process-scheduled-plans \
  --header 'Authorization: Bearer YOUR_CRON_SECRET' \
  --header 'Content-Type: application/json'
```

### Local Development

For local development with Supabase CLI:

```bash
# Start local Supabase
supabase start

# Call the function
curl -X POST http://localhost:54321/functions/v1/process-scheduled-plans \
  --header 'Authorization: Bearer YOUR_CRON_SECRET' \
  --header 'Content-Type: application/json'
```

## Response Format

### Success Response
```json
{
  "success": true,
  "message": "Scheduled plan changes processed successfully",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "results": {
    "processed": 5,
    "errors": 0,
    "cleanedUp": 2,
    "stats": {
      "scheduled": 3,
      "applied": 15,
      "cancelled": 1,
      "totalRevenue": 2500.00
    }
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Internal server error",
  "message": "Detailed error message"
}
```

## Security

- The function uses the service role key for database operations, allowing it to bypass RLS policies
- Optional CRON_SECRET authentication prevents unauthorized access
- All database operations are logged for audit purposes
- CORS headers are properly configured

## Database Operations

The function performs the following database operations:

1. **Fetches** scheduled plan changes that are due (`scheduled_date <= now()`)
2. **Updates** subscription records with new plan details
3. **Marks** scheduled changes as 'applied'
4. **Logs** transactions in the subscription_transactions table
5. **Cleans up** old applied changes (older than 30 days)

## Error Handling

- Individual plan change failures don't stop the entire process
- Detailed error logging for debugging
- Transaction logging errors are non-fatal
- Comprehensive error responses with status codes

## Migration from Next.js API Route

This Edge Function replaces the previous Next.js API route at `/api/cron/process-scheduled-plans`. The functionality is identical but now runs on Supabase Edge Runtime for better integration and performance.

### Key Differences
- Uses Deno runtime instead of Node.js
- Direct Supabase client integration
- Better error handling and logging
- Enhanced response format with detailed statistics
- Improved security with service role authentication
