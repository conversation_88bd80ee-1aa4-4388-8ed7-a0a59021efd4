import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";
import { corsHeaders } from "../_shared/cors.ts";

interface ScheduledPlanChange {
  id: string;
  user_id: string;
  subscription_id: string;
  current_plan_type: string;
  current_billing_cycle: string;
  new_plan_type: string;
  new_billing_cycle: string;
  scheduled_date: string;
  amount_paid: number;
  payment_reference: string;
  status: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  // Only allow POST requests
  if (req.method !== "POST") {
    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      {
        status: 405,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }

  try {
    // Initialize Supabase client with service role key for admin operations
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Missing Supabase configuration");
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Optional: Add authentication/authorization here
    const authHeader = req.headers.get("authorization");
    const cronSecret = Deno.env.get("CRON_SECRET");

    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return new Response(
        JSON.stringify({ error: "Unauthorized" }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" }
        }
      );
    }

    console.log("Processing scheduled plan changes via Edge Function...");

    // Run the scheduled plan processor
    const results = await processScheduledPlanChanges(supabase);

    // Clean up old records
    const cleanedUp = await cleanupOldScheduledChanges(supabase);

    // Get stats
    const stats = await getScheduledPlanChangeStats(supabase);

    console.log("Scheduled plan processor completed:", {
      processed: results.processed,
      errors: results.errors,
      cleanedUp,
      stats
    });

    return new Response(
      JSON.stringify({
        success: true,
        message: "Scheduled plan changes processed successfully",
        timestamp: new Date().toISOString(),
        results: {
          processed: results.processed,
          errors: results.errors,
          cleanedUp,
          stats
        }
      }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );

  } catch (error) {
    console.error("Error processing scheduled plan changes:", error);

    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error"
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" }
      }
    );
  }
});

/**
 * Process all scheduled plan changes that are due
 */
const processScheduledPlanChanges = async (supabase: any): Promise<{
  processed: number;
  errors: number;
  details: Array<{ id: string; success: boolean; error?: string }>;
}> => {
  const results = {
    processed: 0,
    errors: 0,
    details: [] as Array<{ id: string; success: boolean; error?: string }>
  };

  try {
    // Get all scheduled plan changes that are due
    const { data: scheduledChanges, error: fetchError } = await supabase
      .from('scheduled_plan_changes')
      .select('*')
      .eq('status', 'scheduled')
      .lte('scheduled_date', new Date().toISOString());

    if (fetchError) {
      console.error('Error fetching scheduled plan changes:', fetchError);
      return results;
    }

    if (!scheduledChanges || scheduledChanges.length === 0) {
      console.log('No scheduled plan changes to process');
      return results;
    }

    console.log(`Processing ${scheduledChanges.length} scheduled plan changes`);

    // Process each scheduled change
    for (const change of scheduledChanges) {
      try {
        await applyScheduledPlanChange(supabase, change);
        results.processed++;
        results.details.push({ id: change.id, success: true });
        console.log(`Successfully applied scheduled plan change ${change.id}`);
      } catch (error) {
        results.errors++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        results.details.push({ id: change.id, success: false, error: errorMessage });
        console.error(`Error applying scheduled plan change ${change.id}:`, error);
      }
    }

    console.log(`Processed ${results.processed} plan changes, ${results.errors} errors`);
    return results;

  } catch (error) {
    console.error('Error in processScheduledPlanChanges:', error);
    return results;
  }
};

/**
 * Apply a single scheduled plan change
 */
const applyScheduledPlanChange = async (supabase: any, change: ScheduledPlanChange): Promise<void> => {
  const {
    id,
    user_id,
    subscription_id,
    new_plan_type,
    new_billing_cycle,
    scheduled_date,
    amount_paid,
    payment_reference
  } = change;

  // Calculate new period end date
  const startDate = new Date(scheduled_date);
  const endDate = new Date(startDate);

  if (new_billing_cycle === 'yearly') {
    endDate.setFullYear(endDate.getFullYear() + 1);
  } else {
    endDate.setMonth(endDate.getMonth() + 1);
  }

  // Start a transaction-like operation
  try {
    // Update the subscription
    const { error: subscriptionError } = await supabase
      .from('subscriptions')
      .update({
        plan_type: new_plan_type,
        billing_cycle: new_billing_cycle,
        current_period_start: startDate.toISOString(),
        current_period_end: endDate.toISOString(),
        last_payment_date: startDate.toISOString(),
        next_payment_date: endDate.toISOString(),
        has_scheduled_change: false,
        scheduled_plan_type: null,
        scheduled_billing_cycle: null,
        scheduled_change_date: null,
        scheduled_change_amount: null,
        scheduled_change_reference: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', subscription_id);

    if (subscriptionError) {
      throw new Error(`Failed to update subscription: ${subscriptionError.message}`);
    }

    // Mark the scheduled change as applied
    const { error: changeError } = await supabase
      .from('scheduled_plan_changes')
      .update({
        status: 'applied',
        updated_at: new Date().toISOString()
      })
      .eq('id', id);

    if (changeError) {
      throw new Error(`Failed to update scheduled change status: ${changeError.message}`);
    }

    // Log the transaction
    const { error: transactionError } = await supabase
      .from('subscription_transactions')
      .insert({
        user_id,
        subscription_id,
        transaction_type: 'scheduled_change_applied',
        previous_plan: change.current_plan_type,
        new_plan: new_plan_type,
        previous_billing_cycle: change.current_billing_cycle,
        new_billing_cycle: new_billing_cycle,
        amount: amount_paid,
        payment_gateway: 'paystack',
        payment_gateway_reference: payment_reference,
        verification_status: 'verified'
      });

    if (transactionError) {
      console.error('Error logging transaction:', transactionError);
      // Don't throw here as the main operation succeeded
    }

  } catch (error) {
    console.error(`Error applying scheduled plan change ${id}:`, error);
    throw error;
  }
};

/**
 * Clean up old applied scheduled plan changes
 */
const cleanupOldScheduledChanges = async (supabase: any, daysOld: number = 30): Promise<number> => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const { data, error } = await supabase
      .from('scheduled_plan_changes')
      .delete()
      .eq('status', 'applied')
      .lt('updated_at', cutoffDate.toISOString())
      .select('id');

    if (error) {
      console.error('Error cleaning up old scheduled changes:', error);
      return 0;
    }

    const deletedCount = data?.length || 0;
    console.log(`Cleaned up ${deletedCount} old scheduled plan changes`);
    return deletedCount;

  } catch (error) {
    console.error('Error in cleanupOldScheduledChanges:', error);
    return 0;
  }
};

/**
 * Get statistics about scheduled plan changes
 */
const getScheduledPlanChangeStats = async (supabase: any): Promise<{
  scheduled: number;
  applied: number;
  cancelled: number;
  totalRevenue: number;
}> => {
  try {
    const { data, error } = await supabase
      .from('scheduled_plan_changes')
      .select('status, amount_paid');

    if (error) {
      console.error('Error fetching scheduled plan change stats:', error);
      return { scheduled: 0, applied: 0, cancelled: 0, totalRevenue: 0 };
    }

    const stats = {
      scheduled: 0,
      applied: 0,
      cancelled: 0,
      totalRevenue: 0
    };

    data?.forEach((change: any) => {
      switch (change.status) {
        case 'scheduled':
          stats.scheduled++;
          break;
        case 'applied':
          stats.applied++;
          stats.totalRevenue += change.amount_paid || 0;
          break;
        case 'cancelled':
          stats.cancelled++;
          break;
      }
    });

    return stats;

  } catch (error) {
    console.error('Error in getScheduledPlanChangeStats:', error);
    return { scheduled: 0, applied: 0, cancelled: 0, totalRevenue: 0 };
  }
};

// To invoke:
// curl -i --location --request POST 'http://localhost:54321/functions/v1/process-scheduled-plans' \
//   --header 'Authorization: Bearer YOUR_CRON_SECRET' \
//   --header 'Content-Type: application/json'
