-- Create RPC function to get auth users count for admin dashboard
-- This function allows admins to get accurate user counts from auth.users table

CREATE OR REPLACE FUNCTION get_auth_users_count()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_count INTEGER;
BEGIN
    -- Check if the current user is an admin
    -- You can modify this check based on your admin verification logic
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND role = 'admin'
    ) THEN
        -- If not admin, return 0 or raise exception
        RETURN 0;
    END IF;
    
    -- Get count of all users from auth.users
    SELECT COUNT(*) INTO user_count
    FROM auth.users
    WHERE deleted_at IS NULL;
    
    RETURN user_count;
END;
$$;

-- Create RPC function to get new auth users count (last 30 days)
CREATE OR REPLACE FUNCTION get_new_auth_users_count(days_back INTEGER DEFAULT 30)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_count INTEGER;
    cutoff_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND role = 'admin'
    ) THEN
        RETURN 0;
    END IF;
    
    -- Calculate cutoff date
    cutoff_date := NOW() - (days_back || ' days')::INTERVAL;
    
    -- Get count of new users from auth.users
    SELECT COUNT(*) INTO user_count
    FROM auth.users
    WHERE created_at >= cutoff_date
    AND deleted_at IS NULL;
    
    RETURN user_count;
END;
$$;

-- Create RPC function to get comprehensive user statistics
CREATE OR REPLACE FUNCTION get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
    total_users INTEGER;
    new_users INTEGER;
    verified_users INTEGER;
    unverified_users INTEGER;
BEGIN
    -- Check if the current user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND role = 'admin'
    ) THEN
        RETURN '{"error": "Unauthorized"}'::JSON;
    END IF;
    
    -- Get total users count
    SELECT COUNT(*) INTO total_users
    FROM auth.users
    WHERE deleted_at IS NULL;
    
    -- Get new users count (last 30 days)
    SELECT COUNT(*) INTO new_users
    FROM auth.users
    WHERE created_at >= NOW() - INTERVAL '30 days'
    AND deleted_at IS NULL;
    
    -- Get verified users count
    SELECT COUNT(*) INTO verified_users
    FROM auth.users
    WHERE email_confirmed_at IS NOT NULL
    AND deleted_at IS NULL;
    
    -- Get unverified users count
    SELECT COUNT(*) INTO unverified_users
    FROM auth.users
    WHERE email_confirmed_at IS NULL
    AND deleted_at IS NULL;
    
    -- Build result JSON
    result := json_build_object(
        'total_users', total_users,
        'new_users', new_users,
        'verified_users', verified_users,
        'unverified_users', unverified_users,
        'verification_rate', CASE 
            WHEN total_users > 0 THEN ROUND((verified_users::DECIMAL / total_users::DECIMAL) * 100, 2)
            ELSE 0 
        END
    );
    
    RETURN result;
END;
$$;

-- Grant execute permissions to authenticated users (admin check is done within functions)
GRANT EXECUTE ON FUNCTION get_auth_users_count() TO authenticated;
GRANT EXECUTE ON FUNCTION get_new_auth_users_count(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_statistics() TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION get_auth_users_count() IS 'Returns total count of auth users for admin dashboard';
COMMENT ON FUNCTION get_new_auth_users_count(INTEGER) IS 'Returns count of new auth users within specified days for admin dashboard';
COMMENT ON FUNCTION get_user_statistics() IS 'Returns comprehensive user statistics from auth.users for admin dashboard';
