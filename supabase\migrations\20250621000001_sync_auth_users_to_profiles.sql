-- Sync all auth.users to profiles table and set up automatic triggers
-- This ensures we can reliably fetch user counts from profiles table

-- First, let's sync all existing auth.users to profiles table
INSERT INTO profiles (
    id,
    full_name,
    company_name,
    role,
    avatar_url,
    created_at,
    updated_at
)
SELECT 
    au.id,
    COALESCE(
        au.raw_user_meta_data->>'full_name',
        au.raw_user_meta_data->>'name',
        SPLIT_PART(au.email, '@', 1)
    ) as full_name,
    au.raw_user_meta_data->>'company_name' as company_name,
    'freelancer' as role, -- default role
    COALESCE(
        au.raw_user_meta_data->>'avatar_url',
        au.raw_user_meta_data->>'picture'
    ) as avatar_url,
    au.created_at,
    au.updated_at
FROM auth.users au
WHERE au.deleted_at IS NULL
AND NOT EXISTS (
    SELECT 1 FROM profiles p WHERE p.id = au.id
)
ON CONFLICT (id) DO UPDATE SET
    full_name = COALESCE(
        EXCLUDED.full_name,
        profiles.full_name
    ),
    avatar_url = COALESCE(
        EXCLUDED.avatar_url,
        profiles.avatar_url
    ),
    updated_at = NOW();

-- Create or replace function to handle new user signups
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
    INSERT INTO public.profiles (
        id,
        full_name,
        company_name,
        role,
        avatar_url,
        created_at,
        updated_at
    )
    VALUES (
        NEW.id,
        COALESCE(
            NEW.raw_user_meta_data->>'full_name',
            NEW.raw_user_meta_data->>'name',
            SPLIT_PART(NEW.email, '@', 1)
        ),
        NEW.raw_user_meta_data->>'company_name',
        'freelancer', -- default role
        COALESCE(
            NEW.raw_user_meta_data->>'avatar_url',
            NEW.raw_user_meta_data->>'picture'
        ),
        NEW.created_at,
        NEW.updated_at
    )
    ON CONFLICT (id) DO UPDATE SET
        full_name = COALESCE(
            EXCLUDED.full_name,
            profiles.full_name
        ),
        avatar_url = COALESCE(
            EXCLUDED.avatar_url,
            profiles.avatar_url
        ),
        updated_at = NOW();
    
    RETURN NEW;
END;
$$;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create trigger for new user signups
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create or replace function to handle user updates
CREATE OR REPLACE FUNCTION public.handle_user_update()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
    -- Update profile when auth.user is updated
    UPDATE public.profiles SET
        full_name = COALESCE(
            NEW.raw_user_meta_data->>'full_name',
            NEW.raw_user_meta_data->>'name',
            profiles.full_name
        ),
        avatar_url = COALESCE(
            NEW.raw_user_meta_data->>'avatar_url',
            NEW.raw_user_meta_data->>'picture',
            profiles.avatar_url
        ),
        updated_at = NOW()
    WHERE id = NEW.id;
    
    RETURN NEW;
END;
$$;

-- Drop existing update trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_updated ON auth.users;

-- Create trigger for user updates
CREATE TRIGGER on_auth_user_updated
    AFTER UPDATE ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_user_update();

-- Create function to sync user counts and ensure data consistency
CREATE OR REPLACE FUNCTION public.sync_user_data()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
    auth_count INTEGER;
    profile_count INTEGER;
    synced_count INTEGER;
BEGIN
    -- Get count from auth.users
    SELECT COUNT(*) INTO auth_count
    FROM auth.users
    WHERE deleted_at IS NULL;
    
    -- Get count from profiles
    SELECT COUNT(*) INTO profile_count
    FROM profiles;
    
    -- Sync any missing users
    INSERT INTO profiles (
        id,
        full_name,
        role,
        avatar_url,
        created_at,
        updated_at
    )
    SELECT 
        au.id,
        COALESCE(
            au.raw_user_meta_data->>'full_name',
            au.raw_user_meta_data->>'name',
            SPLIT_PART(au.email, '@', 1)
        ) as full_name,
        'freelancer' as role,
        COALESCE(
            au.raw_user_meta_data->>'avatar_url',
            au.raw_user_meta_data->>'picture'
        ) as avatar_url,
        au.created_at,
        au.updated_at
    FROM auth.users au
    WHERE au.deleted_at IS NULL
    AND NOT EXISTS (
        SELECT 1 FROM profiles p WHERE p.id = au.id
    )
    ON CONFLICT (id) DO NOTHING;
    
    -- Get updated profile count
    SELECT COUNT(*) INTO synced_count
    FROM profiles;
    
    -- Build result
    result := json_build_object(
        'auth_users_count', auth_count,
        'profiles_count_before', profile_count,
        'profiles_count_after', synced_count,
        'users_synced', synced_count - profile_count,
        'sync_timestamp', NOW()
    );
    
    RETURN result;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.sync_user_data() TO authenticated;
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO service_role;
GRANT EXECUTE ON FUNCTION public.handle_user_update() TO service_role;

-- Add comments for documentation
COMMENT ON FUNCTION public.handle_new_user() IS 'Automatically creates profile when new user signs up';
COMMENT ON FUNCTION public.handle_user_update() IS 'Automatically updates profile when user metadata changes';
COMMENT ON FUNCTION public.sync_user_data() IS 'Syncs auth.users to profiles and returns sync statistics';

-- Run initial sync and show results
SELECT public.sync_user_data() as initial_sync_result;
